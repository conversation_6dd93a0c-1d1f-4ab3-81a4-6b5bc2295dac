import express from 'express';
import { body } from 'express-validator';
import { validate } from '../middleware/validation';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../shared/types';
import {
  getMySubscriptions,
  getSubscriptionById,
  recordFailure,
  getCoachingEligible,
  getAllSubscriptions
} from '../controllers/subscriptionController';

const router = express.Router();

// Get my subscriptions (student only)
router.get('/my', auth, authorize(UserRole.STUDENT), getMySubscriptions);

// Get all subscriptions (admin only)
router.get('/', auth, authorize(UserRole.ADMIN), getAllSubscriptions);

// Get subscription by ID
router.get('/:id', auth, getSubscriptionById);

// Record assignment failure (internal service call)
router.post('/record-failure', [
  body('subscriptionId').notEmpty().withMessage('Subscription ID is required'),
  validate
], recordFailure);

// Get coaching eligible students (tutor/admin only)
router.get('/coaching/eligible', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), getCoachingEligible);

export default router;
