"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const progressService_1 = require("../services/progressService");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
// Get student progress for a course
router.get('/course/:courseId', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.STUDENT), async (req, res) => {
    try {
        const { courseId } = req.params;
        const studentId = req.user.id;
        const progressDetails = await progressService_1.progressService.getProgressDetails(studentId, courseId);
        res.json({
            success: true,
            data: progressDetails
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting progress:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get pacing status for a course
router.get('/course/:courseId/pacing', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.STUDENT), async (req, res) => {
    try {
        const { courseId } = req.params;
        const { subscriptionStartDate, courseDuration } = req.query;
        const studentId = req.user.id;
        if (!subscriptionStartDate || !courseDuration) {
            return res.status(400).json({
                success: false,
                message: 'Subscription start date and course duration are required'
            });
        }
        const pacing = await progressService_1.progressService.calculatePacing(studentId, courseId, new Date(subscriptionStartDate), parseInt(courseDuration));
        res.json({
            success: true,
            data: pacing
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting pacing status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Complete a lesson
router.post('/lesson/:lessonId/complete', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.STUDENT), async (req, res) => {
    try {
        const { lessonId } = req.params;
        const { courseId } = req.body;
        const studentId = req.user.id;
        await progressService_1.progressService.completeLesson(studentId, courseId, lessonId);
        res.json({
            success: true,
            message: 'Lesson completed successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error completing lesson:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=progress.js.map