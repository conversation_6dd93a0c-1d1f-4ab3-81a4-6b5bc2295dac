"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const dataCollectionService_1 = require("../services/dataCollectionService");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
// Admin dashboard
router.get('/admin', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), async (req, res) => {
    try {
        const dashboard = await dataCollectionService_1.dataCollectionService.getPlatformDashboard();
        res.json({
            success: true,
            data: dashboard
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting admin dashboard:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Tutor dashboard
router.get('/tutor', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR), async (req, res) => {
    try {
        const tutorId = req.user.id;
        const dashboard = await dataCollectionService_1.dataCollectionService.getTutorDashboard(tutorId);
        res.json({
            success: true,
            data: dashboard
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting tutor dashboard:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=dashboard.js.map