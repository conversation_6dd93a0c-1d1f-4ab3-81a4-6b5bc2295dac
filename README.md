# Time Course - English Online Course Platform

A comprehensive microservices-based e-learning platform for English language test preparation with strict evaluation and coaching mechanisms.

## 🚀 Architecture Overview

The platform is built using a microservices architecture with 4 main services:

### 1. Auth & User Service (Port: 3001)
- User authentication (Email/Password + Google OAuth via Firebase)
- User management (Students, Tutors, Admins)
- JWT-based authorization
- Role-based access control

### 2. Course & Learning Service (Port: 3002)
- Course management and content delivery
- Interactive video player with assignment integration
- Progress tracking and pacing system
- Bunny.net integration for secure video streaming
- Coaching session management

### 3. Payment & Subscription Service (Port: 3003)
- Xendit payment gateway integration
- Subscription management with time-based access
- Failure tracking and termination logic
- Webhook handling for payment notifications

### 4. Reporting & Analytics Service (Port: 3004)
- Cross-service data aggregation
- Admin and tutor dashboards
- Performance analytics
- Student progress reports

## 🛠️ Technology Stack

- **Backend Framework**: Express.js with TypeScript
- **Database**: MongoDB (separate instances per service)
- **Authentication**: JWT + Firebase Auth
- **Payment Gateway**: Xendit
- **Video Streaming**: Bunny.net
- **Containerization**: Docker & Docker Compose
- **CI/CD**: GitHub Actions

## 🔧 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 20+ (for development)
- MongoDB (handled by Docker)

### Environment Setup

Create environment files for each service:

```bash
# Copy example env files
cp .env.example .env
```

Required environment variables:
```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# JWT Secret
JWT_SECRET=your_super_secure_jwt_secret

# Xendit Configuration
XENDIT_SECRET_KEY=your_xendit_secret_key
XENDIT_WEBHOOK_TOKEN=your_xendit_webhook_token

# Bunny.net Configuration
BUNNY_API_KEY=your_bunny_api_key
BUNNY_STORAGE_ZONE=your_bunny_storage_zone
```

### Development Setup

1. **Start all services with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

2. **Services will be available at:**
   - API Gateway: http://localhost:80
   - Auth Service: http://localhost:3001
   - Course Service: http://localhost:3002
   - Payment Service: http://localhost:3003
   - Analytics Service: http://localhost:3004

3. **Check service health:**
   ```bash
   curl http://localhost/api/auth/health
   curl http://localhost/api/courses/health
   curl http://localhost/api/payments/health
   curl http://localhost/api/analytics/health
   ```

### Development Mode (Individual Services)

For development, you can run services individually:

```bash
# Install dependencies for all services
cd services/auth-service && npm install
cd ../course-service && npm install
cd ../payment-service && npm install
cd ../analytics-service && npm install

# Run individual services
cd services/auth-service && npm run dev     # Port 3001
cd services/course-service && npm run dev   # Port 3002
cd services/payment-service && npm run dev  # Port 3003
cd services/analytics-service && npm run dev # Port 3004
```

## 📋 Core Features Implementation Status

### ✅ Completed Features

#### Auth & User Service
- [x] User registration with email/password
- [x] Google OAuth integration via Firebase
- [x] JWT-based authentication
- [x] Role-based authorization (Student, Tutor, Admin)
- [x] Admin-only tutor creation
- [x] User profile management
- [x] Account activation/deactivation

#### Infrastructure
- [x] Docker containerization
- [x] Docker Compose orchestration
- [x] MongoDB setup for each service
- [x] API Gateway with Nginx
- [x] GitHub Actions CI/CD pipeline
- [x] Health check endpoints
- [x] Logging and error handling
- [x] Type-safe TypeScript implementation

### 🚧 In Progress / To Be Completed

#### Course & Learning Service
- [ ] Course CRUD operations
- [ ] Module and lesson management
- [ ] Video upload to Bunny.net
- [ ] Secure video streaming (HLS/DASH)
- [ ] Interactive video player
- [ ] Assignment system with timestamp triggers
- [ ] Real-time assignment grading
- [ ] Progress tracking and pacing calculations
- [ ] Certificate generation
- [ ] Coaching session management

#### Payment & Subscription Service
- [ ] Xendit payment integration
- [ ] Subscription creation and management
- [ ] Time-based access control
- [ ] Failure count tracking
- [ ] Auto-termination logic
- [ ] Webhook handling
- [ ] Payment status tracking

#### Analytics & Reporting Service
- [ ] Cross-service data collection
- [ ] Admin dashboard analytics
- [ ] Tutor performance metrics
- [ ] Student progress reports
- [ ] Revenue and subscription analytics
- [ ] Real-time data aggregation

## 🎯 Key Business Logic

### Student Failure System
1. Students fail assignments if score < 100%
2. After 3 failed assignments, subscription is terminated
3. Terminated students are eligible for free coaching
4. System tracks progress pacing against course timeline

### Pacing System
- **Formula**: Expected Progress = (Days Passed / Course Duration) × Total Modules
- **Warning Trigger**: Student completion < Expected Progress
- **Dashboard Alert**: "You are behind schedule! You should have completed X modules by now"

### Security Features
- JWT tokens with expiration
- Rate limiting on all endpoints
- CORS configuration
- Helmet security headers
- Input validation and sanitization
- Role-based route protection

## 🔒 Security Implementation

### Authentication Flow
1. Student registers with email/password or Google OAuth
2. Server validates credentials and creates JWT token
3. Token includes user ID, role, and expiration
4. Protected routes verify token and user permissions
5. Firebase tokens are verified for Google OAuth users

### Authorization Levels
- **Student**: Can access own courses, assignments, progress
- **Tutor**: Can create courses, manage content, view student analytics
- **Admin**: Full system access, user management, platform analytics

## 📊 Database Schema

Each service maintains its own MongoDB database:

### Auth Service Database
- Users collection (authentication and profile data)
- Sessions collection (token blacklisting - future implementation)

### Course Service Database
- Courses collection
- Modules collection
- Lessons collection
- Assignments collection
- Progress collection
- Coaching sessions collection

### Payment Service Database
- Subscriptions collection
- Payments collection
- Transactions collection

### Analytics Service Database
- Aggregated reports and metrics
- Cached analytics data

## 🚀 Deployment

### Production Deployment
The platform is configured for production deployment with:

1. **Docker Multi-stage builds** for optimized images
2. **Environment-based configuration**
3. **Health checks** for container orchestration
4. **Horizontal scaling** capabilities
5. **CI/CD pipeline** with GitHub Actions

### Scaling Considerations
- Each service can be scaled independently
- MongoDB can be sharded for high-volume data
- API Gateway can be load-balanced
- CDN integration for static assets via Bunny.net

## 📝 API Documentation

### Auth Service Endpoints
```
POST /api/auth/register     # User registration
POST /api/auth/login        # Email/password login
POST /api/auth/google       # Google OAuth login
POST /api/auth/logout       # User logout
GET  /api/auth/me          # Get current user
POST /api/auth/refresh     # Refresh JWT token
```

### User Management Endpoints
```
GET    /api/users           # Get all users (Admin)
GET    /api/users/:id       # Get user by ID
PUT    /api/users/:id       # Update user
DELETE /api/users/:id       # Delete user (Admin)
POST   /api/users/tutors    # Create tutor (Admin)
PATCH  /api/users/:id/status # Update user status (Admin)
```

## 🔧 Development Guidelines

### Code Structure
- **Shared types** in `/services/shared/types/`
- **Service-specific logic** in each service directory
- **Common middleware** patterns across services
- **TypeScript strict mode** for type safety

### Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- End-to-end tests for critical user flows
- Performance tests for video streaming

### Monitoring and Logging
- Winston logging for all services
- Health check endpoints
- Performance monitoring (future implementation)
- Error tracking and alerting (future implementation)

## 📋 Next Development Phase

### Priority 1 - Core Learning Features
1. Complete Course Service implementation
2. Implement video streaming with Bunny.net
3. Build interactive assignment system
4. Add progress tracking and pacing

### Priority 2 - Payment Integration
1. Complete Xendit payment integration
2. Implement subscription management
3. Add failure tracking and termination logic
4. Set up webhook handling

### Priority 3 - Analytics & Reporting
1. Implement cross-service data collection
2. Build admin and tutor dashboards
3. Add performance analytics
4. Create automated reporting

### Priority 4 - Advanced Features
1. Mobile app API endpoints
2. Push notifications
3. Advanced analytics
4. Performance optimizations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Follow TypeScript best practices and maintain test coverage
4. Ensure all services pass health checks
5. Submit a pull request with detailed description

## 📞 Support

For technical support or questions about the implementation, please refer to the documentation in each service directory or create an issue in the project repository.