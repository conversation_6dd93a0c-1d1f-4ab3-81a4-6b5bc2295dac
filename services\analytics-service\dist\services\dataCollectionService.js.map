{"version": 3, "file": "dataCollectionService.js", "sourceRoot": "", "sources": ["../../src/services/dataCollectionService.ts"], "names": [], "mappings": ";;;;;;AAiOA,kDAYC;AA7OD,kDAA0B;AAC1B,0DAA6B;AAC7B,mDAAwF;AACxF,4CAAyC;AAEzC,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,uBAAuB,CAAC;AACjF,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,uBAAuB,CAAC;AACrF,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,uBAAuB,CAAC;AAEvF,MAAa,qBAAqB;IAChC;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAE3C,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,oBAAoB,EAAE;aAC5B,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE3B,6BAA6B;YAC7B,MAAM,CAAC,SAAS,EAAE,iBAAiB,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACrE,IAAI,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,uBAAuB,CAAC;gBACjE,IAAI,CAAC,gBAAgB,CAAC,GAAG,mBAAmB,+BAA+B,CAAC;gBAC5E,IAAI,CAAC,gBAAgB,CAAC,GAAG,mBAAmB,sCAAsC,CAAC;aACpF,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,SAAS,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC;YAC/C,MAAM,mBAAmB,GAAG,iBAAiB,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;YAC1H,MAAM,YAAY,GAAG,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,GAAW,EAAE,CAAM,EAAE,EAAE,CAChF,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;YAE3D,sCAAsC;YACtC,MAAM,6BAAiB,CAAC,gBAAgB,CACtC,EAAE,IAAI,EAAE,KAAK,EAAE,EACf;gBACE,UAAU;gBACV,mBAAmB;gBACnB,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,EACD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAC5B,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,+BAA+B,UAAU,WAAW,mBAAmB,uBAAuB,CAAC,CAAC;QAC9G,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,kBAAkB,yBAAyB,CAAC,CAAC;YAChG,MAAM,OAAO,GAAG,WAAW,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC;YAEjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,oCAAoC;gBACpC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACnD,GAAG,mBAAmB,+BAA+B,MAAM,CAAC,EAAE,EAAE,CACjE,CAAC;gBACF,MAAM,aAAa,GAAG,iBAAiB,EAAE,IAAI,EAAE,aAAa,IAAI,EAAE,CAAC;gBAEnE,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC;gBAC9C,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;gBACzF,MAAM,cAAc,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC;oBAC3C,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,oBAAoB,KAAK,GAAG,CAAC,CAAC,MAAM,GAAG,gBAAgB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEnH,MAAM,2BAAe,CAAC,gBAAgB,CACpC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,EACvB;oBACE,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,gBAAgB;oBAChB,iBAAiB;oBACjB,cAAc;oBACd,WAAW,EAAE,IAAI,IAAI,EAAE;iBACxB,EACD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAC5B,CAAC;YACJ,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,uBAAuB,CAAC,CAAC;YAC1F,MAAM,KAAK,GAAG,SAAS,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;YAE3C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC5B,2BAA2B;oBAC3B,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACnD,GAAG,mBAAmB,gCAAgC,IAAI,CAAC,EAAE,EAAE,CAChE,CAAC;oBACF,MAAM,aAAa,GAAG,iBAAiB,EAAE,IAAI,EAAE,aAAa,IAAI,EAAE,CAAC;oBAEnE,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC;oBAC1C,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;oBAC3F,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CACvD,CAAC,CAAC,QAAQ,EAAE,oBAAoB,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;oBAEnD,MAAM,yBAAa,CAAC,gBAAgB,CAClC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EACnB;wBACE,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,YAAY;wBACZ,gBAAgB;wBAChB,mBAAmB;wBACnB,YAAY,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS;wBAC9C,UAAU,EAAE,IAAI,CAAC,SAAS;qBAC3B,EACD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,GAAW;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE;gBACpC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,wBAAwB,GAAG,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAE3E,MAAM,CAAC,eAAe,EAAE,YAAY,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpE,6BAAiB,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;gBAC9C,6BAAiB,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;iBAC9B,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;gBACpB,2BAAe,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;aAChE,CAAC,CAAC;YAEH,OAAO;gBACL,QAAQ,EAAE,eAAe,IAAI,EAAE;gBAC/B,YAAY,EAAE,YAAY,IAAI,EAAE;gBAChC,UAAU,EAAE,UAAU,IAAI,EAAE;aAC7B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAe;QACrC,IAAI,CAAC;YACH,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACtD,2BAAe,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC;gBAChE,2BAAe,CAAC,SAAS,CAAC;oBACxB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE;oBACvB,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAAE,EAAE;iBAChE,CAAC;aACH,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,YAAY,IAAI,EAAE;gBAC3B,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;gBAC3C,YAAY,EAAE,YAAY,CAAC,MAAM;aAClC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAjND,sDAiNC;AAEY,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;AAEjE;;GAEG;AACH,SAAgB,mBAAmB;IACjC,iCAAiC;IACjC,mBAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QACpC,MAAM,6BAAqB,CAAC,cAAc,EAAE,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,6BAAqB,CAAC,cAAc,EAAE,CAAC;IAC/C,CAAC,EAAE,KAAK,CAAC,CAAC;IAEV,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;AACjD,CAAC"}