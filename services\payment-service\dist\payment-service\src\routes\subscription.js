"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const subscriptionController_1 = require("../controllers/subscriptionController");
const router = express_1.default.Router();
// Get my subscriptions (student only)
router.get('/my', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.STUDENT), subscriptionController_1.getMySubscriptions);
// Get all subscriptions (admin only)
router.get('/', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), subscriptionController_1.getAllSubscriptions);
// Get subscription by ID
router.get('/:id', auth_1.auth, subscriptionController_1.getSubscriptionById);
// Record assignment failure (internal service call)
router.post('/record-failure', [
    (0, express_validator_1.body)('subscriptionId').notEmpty().withMessage('Subscription ID is required'),
    validation_1.validate
], subscriptionController_1.recordFailure);
// Get coaching eligible students (tutor/admin only)
router.get('/coaching/eligible', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), subscriptionController_1.getCoachingEligible);
exports.default = router;
//# sourceMappingURL=subscription.js.map