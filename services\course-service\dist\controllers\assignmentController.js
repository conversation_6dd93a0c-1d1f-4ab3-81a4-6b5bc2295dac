"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteAssignment = exports.updateAssignment = exports.getAssignmentSubmissions = exports.submitAssignment = exports.getAssignmentById = exports.getAssignmentsByLesson = exports.createAssignment = void 0;
const Assignment_1 = require("../models/Assignment");
const AssignmentSubmission_1 = require("../models/AssignmentSubmission");
const Lesson_1 = require("../models/Lesson");
const progressService_1 = require("../services/progressService");
const types_1 = require("../shared/types");
const logger_1 = require("../utils/logger");
const axios_1 = __importDefault(require("axios"));
const createAssignment = async (req, res) => {
    try {
        const { lessonId, title, description, questions, triggerTime, timeLimit, passingScore, maxAttempts } = req.body;
        // Verify lesson exists and user has permission
        const lesson = await Lesson_1.Lesson.findById(lessonId);
        if (!lesson) {
            return res.status(404).json({
                success: false,
                message: 'Lesson not found'
            });
        }
        // Only tutors and admins can create assignments
        if (req.user.role !== types_1.UserRole.TUTOR && req.user.role !== types_1.UserRole.ADMIN) {
            return res.status(403).json({
                success: false,
                message: 'Only tutors and admins can create assignments'
            });
        }
        const assignment = new Assignment_1.Assignment({
            lessonId,
            title,
            description,
            questions: questions.map((q, index) => ({
                ...q,
                order: index + 1
            })),
            triggerTime,
            timeLimit,
            passingScore: passingScore || 100, // Default to 100% for strict evaluation
            maxAttempts: maxAttempts || 1
        });
        await assignment.save();
        logger_1.logger.info(`Assignment created: ${assignment.title} for lesson ${lessonId}`);
        res.status(201).json({
            success: true,
            data: assignment,
            message: 'Assignment created successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error creating assignment:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.createAssignment = createAssignment;
const getAssignmentsByLesson = async (req, res) => {
    try {
        const { lessonId } = req.params;
        const assignments = await Assignment_1.Assignment.find({ lessonId, isActive: true })
            .sort({ triggerTime: 1 });
        res.json({
            success: true,
            data: assignments
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting assignments by lesson:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getAssignmentsByLesson = getAssignmentsByLesson;
const getAssignmentById = async (req, res) => {
    try {
        const { id } = req.params;
        const assignment = await Assignment_1.Assignment.findById(id);
        if (!assignment) {
            return res.status(404).json({
                success: false,
                message: 'Assignment not found'
            });
        }
        res.json({
            success: true,
            data: assignment
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting assignment by ID:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getAssignmentById = getAssignmentById;
const submitAssignment = async (req, res) => {
    try {
        const { id } = req.params;
        const { answers, subscriptionId, timeSpent } = req.body;
        const studentId = req.user.id;
        // Get assignment
        const assignment = await Assignment_1.Assignment.findById(id);
        if (!assignment) {
            return res.status(404).json({
                success: false,
                message: 'Assignment not found'
            });
        }
        // Check if student has already reached max attempts
        const previousAttempts = await AssignmentSubmission_1.AssignmentSubmission.countDocuments({
            studentId,
            assignmentId: id
        });
        if (previousAttempts >= assignment.maxAttempts) {
            return res.status(400).json({
                success: false,
                message: 'Maximum attempts reached for this assignment'
            });
        }
        // Grade the assignment
        const gradedAnswers = assignment.questions.map(question => {
            const studentAnswer = answers.find((a) => a.questionId === question.id);
            const isCorrect = studentAnswer &&
                studentAnswer.answer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
            return {
                questionId: question.id,
                answer: studentAnswer?.answer || '',
                isCorrect,
                points: isCorrect ? question.points : 0
            };
        });
        // Calculate score
        const totalPoints = assignment.questions.reduce((sum, q) => sum + q.points, 0);
        const earnedPoints = gradedAnswers.reduce((sum, a) => sum + a.points, 0);
        const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;
        const isPassed = score >= assignment.passingScore;
        // Create submission record
        const submission = new AssignmentSubmission_1.AssignmentSubmission({
            studentId,
            assignmentId: id,
            subscriptionId,
            answers: gradedAnswers,
            score,
            isPassed,
            attemptNumber: previousAttempts + 1,
            timeSpent
        });
        await submission.save();
        // Update progress if passed
        if (isPassed) {
            const lesson = await Lesson_1.Lesson.findById(assignment.lessonId);
            if (lesson) {
                // Get course ID from lesson's module
                const { Module } = require('../models/Module');
                const module = await Module.findById(lesson.moduleId);
                if (module) {
                    await progressService_1.progressService.completeAssignment(studentId, module.courseId, id);
                }
            }
        }
        else {
            // Record failure in payment service
            try {
                const paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3003';
                await axios_1.default.post(`${paymentServiceUrl}/api/subscriptions/record-failure`, {
                    subscriptionId
                });
                logger_1.logger.info(`Assignment failure recorded for subscription: ${subscriptionId}`);
            }
            catch (error) {
                logger_1.logger.error('Error recording assignment failure:', error);
            }
        }
        logger_1.logger.info(`Assignment submitted: ${id} by student ${studentId}, score: ${score}%`);
        res.json({
            success: true,
            data: {
                submission,
                isPassed,
                score,
                feedback: isPassed ? 'Congratulations! You passed the assignment.' :
                    `You scored ${score}%. You need ${assignment.passingScore}% to pass.`
            },
            message: isPassed ? 'Assignment passed successfully' : 'Assignment failed'
        });
    }
    catch (error) {
        logger_1.logger.error('Error submitting assignment:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.submitAssignment = submitAssignment;
const getAssignmentSubmissions = async (req, res) => {
    try {
        const { id } = req.params;
        const studentId = req.user.id;
        const submissions = await AssignmentSubmission_1.AssignmentSubmission.find({
            assignmentId: id,
            studentId
        }).sort({ submittedAt: -1 });
        res.json({
            success: true,
            data: submissions
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting assignment submissions:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getAssignmentSubmissions = getAssignmentSubmissions;
const updateAssignment = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;
        // Only tutors and admins can update assignments
        if (req.user.role !== types_1.UserRole.TUTOR && req.user.role !== types_1.UserRole.ADMIN) {
            return res.status(403).json({
                success: false,
                message: 'Only tutors and admins can update assignments'
            });
        }
        const assignment = await Assignment_1.Assignment.findByIdAndUpdate(id, { $set: updates }, { new: true, runValidators: true });
        if (!assignment) {
            return res.status(404).json({
                success: false,
                message: 'Assignment not found'
            });
        }
        res.json({
            success: true,
            data: assignment,
            message: 'Assignment updated successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error updating assignment:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.updateAssignment = updateAssignment;
const deleteAssignment = async (req, res) => {
    try {
        const { id } = req.params;
        // Only tutors and admins can delete assignments
        if (req.user.role !== types_1.UserRole.TUTOR && req.user.role !== types_1.UserRole.ADMIN) {
            return res.status(403).json({
                success: false,
                message: 'Only tutors and admins can delete assignments'
            });
        }
        const assignment = await Assignment_1.Assignment.findByIdAndDelete(id);
        if (!assignment) {
            return res.status(404).json({
                success: false,
                message: 'Assignment not found'
            });
        }
        // Also delete all submissions for this assignment
        await AssignmentSubmission_1.AssignmentSubmission.deleteMany({ assignmentId: id });
        res.json({
            success: true,
            message: 'Assignment deleted successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error deleting assignment:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.deleteAssignment = deleteAssignment;
//# sourceMappingURL=assignmentController.js.map