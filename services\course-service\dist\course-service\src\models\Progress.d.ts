import mongoose, { Document } from "mongoose";
import { Progress as IProgress } from "../shared/types";
export interface ProgressDocument extends IProgress, Document {
}
export declare const Progress: mongoose.Model<ProgressDocument, {}, {}, {}, mongoose.Document<unknown, {}, ProgressDocument, {}, {}> & ProgressDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Progress.d.ts.map