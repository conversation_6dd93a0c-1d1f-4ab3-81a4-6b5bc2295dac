import { Request, Response } from "express";
interface AuthRequest extends Request {
    user?: any;
}
export declare const createPayment: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getPaymentStatus: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getMyPayments: (req: AuthRequest, res: Response) => Promise<void>;
export {};
//# sourceMappingURL=paymentController.d.ts.map