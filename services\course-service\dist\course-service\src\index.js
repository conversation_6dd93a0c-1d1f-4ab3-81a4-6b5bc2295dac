"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const dotenv_1 = __importDefault(require("dotenv"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const database_1 = require("./config/database");
const course_1 = __importDefault(require("./routes/course"));
const module_1 = __importDefault(require("./routes/module"));
const lesson_1 = __importDefault(require("./routes/lesson"));
const assignment_1 = __importDefault(require("./routes/assignment"));
const progress_1 = __importDefault(require("./routes/progress"));
const coaching_1 = __importDefault(require("./routes/coaching"));
const health_1 = __importDefault(require("./routes/health"));
const errorHandler_1 = require("./middleware/errorHandler");
const logger_1 = require("./utils/logger");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3002;
// Security middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));
// Rate limiting
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Routes
app.use('/api/courses', course_1.default);
app.use('/api/modules', module_1.default);
app.use('/api/lessons', lesson_1.default);
app.use('/api/assignments', assignment_1.default);
app.use('/api/progress', progress_1.default);
app.use('/api/coaching', coaching_1.default);
app.use('/health', health_1.default);
// Error handling middleware
app.use(errorHandler_1.errorHandler);
// Start server
async function startServer() {
    try {
        await (0, database_1.connectDB)();
        app.listen(PORT, () => {
            logger_1.logger.info(`Course Service running on port ${PORT}`);
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
}
startServer();
exports.default = app;
//# sourceMappingURL=index.js.map