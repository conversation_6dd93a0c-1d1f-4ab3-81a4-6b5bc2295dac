"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Subscription = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const types_1 = require("../shared/types");
const subscriptionSchema = new mongoose_1.Schema({
    studentId: { type: String, required: true, index: true },
    courseId: { type: String, required: true, index: true },
    status: {
        type: String,
        enum: Object.values(types_1.SubscriptionStatus),
        default: types_1.SubscriptionStatus.ACTIVE,
    },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    failureCount: { type: Number, default: 0, min: 0 },
    maxFailures: { type: Number, default: 3 },
    paymentId: { type: String },
    isEligibleForCoaching: { type: Boolean, default: false },
    terminatedAt: { type: Date },
    terminationReason: { type: String },
    progress: {
        completedLessons: { type: Number, default: 0 },
        totalLessons: { type: Number, default: 0 },
        lastAccessedAt: { type: Date },
    },
}, {
    timestamps: true,
});
// Compound index for student and course
subscriptionSchema.index({ studentId: 1, courseId: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ endDate: 1 });
// Update updatedAt field
subscriptionSchema.pre("save", function (next) {
    this.updatedAt = new Date();
    next();
});
exports.Subscription = mongoose_1.default.model("Subscription", subscriptionSchema);
//# sourceMappingURL=Subscription.js.map