import express from 'express';
import { body } from 'express-validator';
import { validate } from '../middleware/validation';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../shared/types';
import {
  createCourse,
  getAllCourses,
  getCourseById,
  updateCourse,
  deleteCourse,
  publishCourse,
  getMyCourses
} from '../controllers/courseController';

const router = express.Router();

// Get all courses (public)
router.get('/', getAllCourses);

// Get course by ID (public)
router.get('/:id', getCourseById);

// Get my courses (tutor/admin only)
router.get('/my/courses', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), getMyCourses);

// Create course (tutor/admin only)
router.post('/', [
  auth,
  authorize(UserRole.TUTOR, UserRole.ADMIN),
  body('title').notEmpty().withMessage('Course title is required'),
  body('description').notEmpty().withMessage('Course description is required'),
  body('category').notEmpty().withMessage('Course category is required'),
  body('duration').isInt({ min: 1 }).withMessage('Duration must be a positive integer'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  validate
], createCourse);

// Update course (tutor/admin only)
router.put('/:id', [
  auth,
  authorize(UserRole.TUTOR, UserRole.ADMIN),
  validate
], updateCourse);

// Delete course (tutor/admin only)
router.delete('/:id', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), deleteCourse);

// Publish course (tutor/admin only)
router.patch('/:id/publish', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), publishCourse);

export default router;
