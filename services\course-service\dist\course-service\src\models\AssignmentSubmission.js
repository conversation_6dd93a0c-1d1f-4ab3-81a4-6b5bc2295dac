"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssignmentSubmission = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const answerSchema = new mongoose_1.Schema({
    questionId: { type: String, required: true },
    answer: { type: String, required: true },
    isCorrect: { type: Boolean, required: true },
    points: { type: Number, required: true, min: 0 }
});
const assignmentSubmissionSchema = new mongoose_1.Schema({
    studentId: { type: String, required: true, index: true },
    assignmentId: { type: String, required: true, index: true },
    subscriptionId: { type: String, required: true },
    answers: [answerSchema],
    score: { type: Number, required: true, min: 0, max: 100 },
    isPassed: { type: Boolean, required: true },
    attemptNumber: { type: Number, required: true, min: 1 },
    timeSpent: { type: Number }, // in seconds
    submittedAt: { type: Date, default: Date.now },
    gradedAt: { type: Date, default: Date.now },
    feedback: { type: String }
});
// Compound indexes
assignmentSubmissionSchema.index({ studentId: 1, assignmentId: 1, attemptNumber: 1 }, { unique: true });
assignmentSubmissionSchema.index({ subscriptionId: 1 });
assignmentSubmissionSchema.index({ submittedAt: -1 });
exports.AssignmentSubmission = mongoose_1.default.model('AssignmentSubmission', assignmentSubmissionSchema);
//# sourceMappingURL=AssignmentSubmission.js.map