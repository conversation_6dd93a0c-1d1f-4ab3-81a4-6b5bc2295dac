"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const authController_1 = require("../controllers/authController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = express_1.default.Router();
// Registration
router.post('/register', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
    (0, express_validator_1.body)('profile.firstName').notEmpty().withMessage('First name is required'),
    (0, express_validator_1.body)('profile.lastName').notEmpty().withMessage('Last name is required'),
    validation_1.validate
], authController_1.register);
// Login
router.post('/login', [
    (0, express_validator_1.body)('email').isEmail().normalizeEmail(),
    (0, express_validator_1.body)('password').notEmpty().withMessage('Password is required'),
    validation_1.validate
], authController_1.login);
// Google Login
router.post('/google', [
    (0, express_validator_1.body)('idToken').notEmpty().withMessage('Firebase ID token is required'),
    validation_1.validate
], authController_1.googleLogin);
// Logout
router.post('/logout', auth_1.auth, authController_1.logout);
// Refresh Token
router.post('/refresh', authController_1.refreshToken);
// Verify Email
router.get('/verify/:token', authController_1.verifyEmail);
// Get current user
router.get('/me', auth_1.auth, (req, res) => {
    res.json({
        success: true,
        data: req.user
    });
});
exports.default = router;
//# sourceMappingURL=auth.js.map