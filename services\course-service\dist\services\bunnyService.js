"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.bunnyService = exports.BunnyService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class BunnyService {
    constructor() {
        this.apiKey = process.env.BUNNY_API_KEY || '';
        this.storageZone = process.env.BUNNY_STORAGE_ZONE || '';
        this.videoLibraryId = process.env.BUNNY_VIDEO_LIBRARY_ID || '';
        this.baseUrl = 'https://video.bunnycdn.com';
        if (!this.apiKey || !this.storageZone) {
            throw new Error('Bunny.net configuration is missing');
        }
    }
    /**
     * Upload video to Bunny.net
     */
    async uploadVideo(videoBuffer, title) {
        try {
            const response = await axios_1.default.post(`${this.baseUrl}/library/${this.videoLibraryId}/videos`, {
                title: title
            }, {
                headers: {
                    'AccessKey': this.apiKey,
                    'Content-Type': 'application/json'
                }
            });
            const videoId = response.data.guid;
            // Upload the actual video file
            await axios_1.default.put(`${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}`, videoBuffer, {
                headers: {
                    'AccessKey': this.apiKey,
                    'Content-Type': 'application/octet-stream'
                }
            });
            logger_1.logger.info(`Video uploaded successfully: ${videoId}`);
            return videoId;
        }
        catch (error) {
            logger_1.logger.error('Error uploading video to Bunny.net:', error);
            throw new Error('Failed to upload video');
        }
    }
    /**
     * Get streaming URLs for a video
     */
    async getStreamingUrls(videoId) {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}`, {
                headers: {
                    'AccessKey': this.apiKey
                }
            });
            const video = response.data;
            return {
                hlsUrl: `https://iframe.mediadelivery.net/embed/${this.videoLibraryId}/${videoId}`,
                dashUrl: `https://video.bunnycdn.com/play/${this.videoLibraryId}/${videoId}`,
                mp4Url: video.mp4Url || '',
                thumbnailUrl: video.thumbnailUrl || ''
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting streaming URLs:', error);
            throw new Error('Failed to get streaming URLs');
        }
    }
    /**
     * Generate secure video token for authenticated access
     */
    generateSecureToken(videoId, userId, expirationTime) {
        const expiry = expirationTime || Math.floor(Date.now() / 1000) + (24 * 60 * 60); // 24 hours
        const tokenData = {
            videoId,
            userId,
            exp: expiry
        };
        // In production, use proper JWT signing
        return Buffer.from(JSON.stringify(tokenData)).toString('base64');
    }
    /**
     * Delete video from Bunny.net
     */
    async deleteVideo(videoId) {
        try {
            await axios_1.default.delete(`${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}`, {
                headers: {
                    'AccessKey': this.apiKey
                }
            });
            logger_1.logger.info(`Video deleted successfully: ${videoId}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Error deleting video:', error);
            return false;
        }
    }
    /**
     * Get video analytics
     */
    async getVideoAnalytics(videoId) {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}/statistics`, {
                headers: {
                    'AccessKey': this.apiKey
                }
            });
            return response.data;
        }
        catch (error) {
            logger_1.logger.error('Error getting video analytics:', error);
            throw new Error('Failed to get video analytics');
        }
    }
}
exports.BunnyService = BunnyService;
exports.bunnyService = new BunnyService();
//# sourceMappingURL=bunnyService.js.map