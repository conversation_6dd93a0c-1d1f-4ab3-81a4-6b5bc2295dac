{"info": {"name": "Time Course - English Online Course Platform API", "description": "Complete API collection for Time Course microservices platform with authentication, course management, payments, and analytics", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "course_id", "value": "", "type": "string"}, {"key": "subscription_id", "value": "", "type": "string"}], "item": [{"name": "📊 Analytics Service", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/analytics/health", "host": ["{{base_url}}"], "path": ["api", "analytics", "health"]}}}, {"name": "Trigger Data Collection (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/collect", "host": ["{{base_url}}"], "path": ["api", "analytics", "collect"]}}}, {"name": "Get Platform Analytics (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/platform", "host": ["{{base_url}}"], "path": ["api", "analytics", "platform"]}}}, {"name": "Get Tutor Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/analytics/tutor/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "analytics", "tutor", "{{user_id}}"]}}}]}, {"name": "📈 Dashboard Service", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/admin", "host": ["{{base_url}}"], "path": ["api", "dashboard", "admin"]}}}, {"name": "Tutor Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/tutor", "host": ["{{base_url}}"], "path": ["api", "dashboard", "tutor"]}}}]}, {"name": "📋 Reports Service", "item": [{"name": "Platform Report (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/reports/platform?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{base_url}}"], "path": ["api", "reports", "platform"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}]}}}, {"name": "Course Performance Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/reports/courses", "host": ["{{base_url}}"], "path": ["api", "reports", "courses"]}}}, {"name": "User Activity Report (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/reports/users?role=student", "host": ["{{base_url}}"], "path": ["api", "reports", "users"], "query": [{"key": "role", "value": "student"}]}}}]}, {"name": "🔐 Authentication Service", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/health", "host": ["{{base_url}}"], "path": ["api", "auth", "health"]}}}, {"name": "Register Student", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.token);", "    pm.collectionVariables.set('user_id', response.data.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"profile\": {\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Doe\",\n    \"country\": \"Indonesia\",\n    \"language\": \"en\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.token);", "    pm.collectionVariables.set('user_id', response.data.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Google Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"idToken\": \"your_firebase_id_token_here\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/google", "host": ["{{base_url}}"], "path": ["api", "auth", "google"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/auth/me", "host": ["{{base_url}}"], "path": ["api", "auth", "me"]}}}, {"name": "Create <PERSON><PERSON> (Admin Only)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"profile\": {\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"country\": \"Indonesia\",\n    \"language\": \"en\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/users/tutors", "host": ["{{base_url}}"], "path": ["api", "users", "tutors"]}}}]}, {"name": "📚 Course Service", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/courses/health", "host": ["{{base_url}}"], "path": ["api", "courses", "health"]}}}, {"name": "Get All Courses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/courses?page=1&limit=10&category=ielts", "host": ["{{base_url}}"], "path": ["api", "courses"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "ielts"}]}}}, {"name": "Create Course (Tutor/Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('course_id', response.data.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"IELTS Preparation Course\",\n  \"description\": \"Comprehensive IELTS preparation with practice tests and assignments\",\n  \"category\": \"ielts\",\n  \"duration\": 90,\n  \"price\": 500000,\n  \"currency\": \"IDR\",\n  \"tags\": [\"ielts\", \"english\", \"test-prep\"],\n  \"requirements\": [\"Basic English knowledge\", \"Computer with internet\"],\n  \"learningOutcomes\": [\"Pass IELTS with band 7+\", \"Improve English skills\"]\n}"}, "url": {"raw": "{{base_url}}/api/courses", "host": ["{{base_url}}"], "path": ["api", "courses"]}}}, {"name": "Get Course by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/courses/{{course_id}}", "host": ["{{base_url}}"], "path": ["api", "courses", "{{course_id}}"]}}}, {"name": "Update Course", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Advanced IELTS Preparation Course\",\n  \"price\": 750000\n}"}, "url": {"raw": "{{base_url}}/api/courses/{{course_id}}", "host": ["{{base_url}}"], "path": ["api", "courses", "{{course_id}}"]}}}, {"name": "Publish Course", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/courses/{{course_id}}/publish", "host": ["{{base_url}}"], "path": ["api", "courses", "{{course_id}}", "publish"]}}}, {"name": "Get My Courses (<PERSON><PERSON>)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/courses/my/courses", "host": ["{{base_url}}"], "path": ["api", "courses", "my", "courses"]}}}]}, {"name": "📝 Assignment Service", "item": [{"name": "Create Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"lessonId\": \"lesson_id_here\",\n  \"title\": \"Grammar Quiz\",\n  \"description\": \"Test your understanding of English grammar\",\n  \"questions\": [\n    {\n      \"text\": \"What is the past tense of 'go'?\",\n      \"type\": \"short_answer\",\n      \"correctAnswer\": \"went\",\n      \"points\": 10\n    },\n    {\n      \"text\": \"Choose the correct article: ___ apple\",\n      \"type\": \"multiple_choice\",\n      \"options\": [\"a\", \"an\", \"the\"],\n      \"correctAnswer\": \"an\",\n      \"points\": 10\n    }\n  ],\n  \"triggerTime\": 300,\n  \"timeLimit\": 10,\n  \"passingScore\": 100\n}"}, "url": {"raw": "{{base_url}}/api/assignments", "host": ["{{base_url}}"], "path": ["api", "assignments"]}}}, {"name": "Submit Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"answers\": [\n    {\n      \"questionId\": \"question_id_1\",\n      \"answer\": \"went\"\n    },\n    {\n      \"questionId\": \"question_id_2\",\n      \"answer\": \"an\"\n    }\n  ],\n  \"subscriptionId\": \"{{subscription_id}}\",\n  \"timeSpent\": 480\n}"}, "url": {"raw": "{{base_url}}/api/assignments/assignment_id_here/submit", "host": ["{{base_url}}"], "path": ["api", "assignments", "assignment_id_here", "submit"]}}}, {"name": "Get Assignment Submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/assignments/assignment_id_here/submissions", "host": ["{{base_url}}"], "path": ["api", "assignments", "assignment_id_here", "submissions"]}}}]}, {"name": "📊 Progress Tracking", "item": [{"name": "Get Course Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/progress/course/{{course_id}}", "host": ["{{base_url}}"], "path": ["api", "progress", "course", "{{course_id}}"]}}}, {"name": "Get Pacing Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/progress/course/{{course_id}}/pacing?subscriptionStartDate=2024-01-01&courseDuration=90", "host": ["{{base_url}}"], "path": ["api", "progress", "course", "{{course_id}}", "pacing"], "query": [{"key": "subscriptionStartDate", "value": "2024-01-01"}, {"key": "courseDuration", "value": "90"}]}}}, {"name": "Complete Lesson", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"{{course_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/progress/lesson/lesson_id_here/complete", "host": ["{{base_url}}"], "path": ["api", "progress", "lesson", "lesson_id_here", "complete"]}}}]}, {"name": "💳 Payment Service", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/payments/health", "host": ["{{base_url}}"], "path": ["api", "payments", "health"]}}}, {"name": "Create Payment", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('payment_id', response.data.paymentId);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"{{course_id}}\",\n  \"amount\": 500000,\n  \"courseDuration\": 90\n}"}, "url": {"raw": "{{base_url}}/api/payments", "host": ["{{base_url}}"], "path": ["api", "payments"]}}}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/payments/{{payment_id}}", "host": ["{{base_url}}"], "path": ["api", "payments", "{{payment_id}}"]}}}, {"name": "Get My Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/payments/my/payments", "host": ["{{base_url}}"], "path": ["api", "payments", "my", "payments"]}}}]}, {"name": "📋 Subscription Service", "item": [{"name": "Get My Subscriptions", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data.subscriptions.length > 0) {", "        pm.collectionVariables.set('subscription_id', response.data.subscriptions[0].id);", "    }", "}"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions/my", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "my"]}}}, {"name": "Get Subscription by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions/{{subscription_id}}", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "{{subscription_id}}"]}}}, {"name": "Record Assignment Failure", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subscriptionId\": \"{{subscription_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/subscriptions/record-failure", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "record-failure"]}}}, {"name": "Get Coaching Eligible Students (Tutor/Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions/coaching/eligible?courseId={{course_id}}", "host": ["{{base_url}}"], "path": ["api", "subscriptions", "coaching", "eligible"], "query": [{"key": "courseId", "value": "{{course_id}}"}]}}}, {"name": "Get All Subscriptions (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/subscriptions?status=active&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "subscriptions"], "query": [{"key": "status", "value": "active"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}, {"name": "🔗 Webhook Endpoints", "item": [{"name": "Xendit Payment Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-callback-token", "value": "your_xendit_webhook_token"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"invoice_id_from_xendit\",\n  \"external_id\": \"TC-1234567890-ABC123\",\n  \"status\": \"PAID\",\n  \"amount\": 500000,\n  \"paid_at\": \"2024-01-15T10:30:00.000Z\",\n  \"event\": \"invoice.paid\"\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/xendit", "host": ["{{base_url}}"], "path": ["api", "webhooks", "xendit"]}}}]}, {"name": "👥 User Management", "item": [{"name": "Get All Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/users?role=student&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "users"], "query": [{"key": "role", "value": "student"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"profile\": {\n    \"firstName\": \"John Updated\",\n    \"lastName\": \"<PERSON>e Updated\",\n    \"phone\": \"+62812345678\",\n    \"country\": \"Indonesia\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}}}, {"name": "Update User Status (Admin)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": false\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}/status", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "status"]}}}]}, {"name": "🏥 Health Checks", "item": [{"name": "Auth Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/health", "host": ["{{base_url}}"], "path": ["api", "auth", "health"]}}}, {"name": "Course Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/courses/health", "host": ["{{base_url}}"], "path": ["api", "courses", "health"]}}}, {"name": "Payment Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/payments/health", "host": ["{{base_url}}"], "path": ["api", "payments", "health"]}}}, {"name": "Analytics Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/analytics/health", "host": ["{{base_url}}"], "path": ["api", "analytics", "health"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set authorization header if token exists", "const token = pm.collectionVariables.get('auth_token');", "if (token && !pm.request.headers.has('Authorization')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: `Bear<PERSON> ${token}`", "    });", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to check response format", "pm.test('Response should be JSON', function () {", "    pm.response.to.be.json;", "});", "", "pm.test('Response should have success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});"]}}]}