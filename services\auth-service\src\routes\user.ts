import express from "express";
import { body } from "express-validator";
import { auth, authorize } from "../middleware/auth";
import { validate } from "../middleware/validation";
import { UserRole } from "../shared/types";
import {
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
  createTutor,
  updateUserStatus,
} from "../controllers/userController";

const router = express.Router();

// Get all users (Admin only)
router.get("/", auth, authorize(UserRole.ADMIN), getAllUsers);

// Get user by ID
router.get("/:id", auth, getUserById);

// Update user
router.put(
  "/:id",
  [
    auth,
    body("profile.firstName").optional().notEmpty(),
    body("profile.lastName").optional().notEmpty(),
    body("profile.phone").optional().isMobilePhone("any"),
    validate,
  ],
  updateUser
);

// Delete user (Admin only)
router.delete("/:id", auth, authorize(UserRole.ADMIN), deleteUser);

// Create tutor (Admin only)
router.post(
  "/tutors",
  [
    auth,
    authorize(UserRole.ADMIN),
    body("email").isEmail().normalizeEmail(),
    body("password").isLength({ min: 8 }),
    body("profile.firstName").notEmpty(),
    body("profile.lastName").notEmpty(),
    validate,
  ],
  createTutor
);

// Update user status (Admin only)
router.patch(
  "/:id/status",
  [auth, authorize(UserRole.ADMIN), body("isActive").isBoolean(), validate],
  updateUserStatus
);

export default router;
