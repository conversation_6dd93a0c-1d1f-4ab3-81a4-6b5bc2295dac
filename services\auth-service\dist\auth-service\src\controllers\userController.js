"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserStatus = exports.createTutor = exports.deleteUser = exports.updateUser = exports.getUserById = exports.getAllUsers = void 0;
const User_1 = require("../models/User");
const types_1 = require("../shared/types");
const logger_1 = require("../utils/logger");
const getAllUsers = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const role = req.query.role;
        const filter = {};
        if (role) {
            filter.role = role;
        }
        const users = await User_1.User.find(filter)
            .select("-password")
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ createdAt: -1 });
        const total = await User_1.User.countDocuments(filter);
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                },
            },
        });
    }
    catch (error) {
        logger_1.logger.error("Get all users error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.getAllUsers = getAllUsers;
const getUserById = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User_1.User.findById(id).select("-password");
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }
        res.json({
            success: true,
            data: user,
        });
    }
    catch (error) {
        logger_1.logger.error("Get user by ID error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.getUserById = getUserById;
const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;
        // Users can only update their own profile unless they're admin
        const requestingUser = req.user;
        if (requestingUser.role !== types_1.UserRole.ADMIN && requestingUser.id !== id) {
            return res.status(403).json({
                success: false,
                message: "You can only update your own profile",
            });
        }
        const user = await User_1.User.findByIdAndUpdate(id, { $set: updates }, { new: true, runValidators: true }).select("-password");
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }
        res.json({
            success: true,
            data: user,
            message: "User updated successfully",
        });
    }
    catch (error) {
        logger_1.logger.error("Update user error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.updateUser = updateUser;
const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User_1.User.findByIdAndDelete(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }
        res.json({
            success: true,
            message: "User deleted successfully",
        });
    }
    catch (error) {
        logger_1.logger.error("Delete user error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.deleteUser = deleteUser;
const createTutor = async (req, res) => {
    try {
        const { email, password, profile } = req.body;
        // Check if user already exists
        const existingUser = await User_1.User.findOne({ email });
        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: "User already exists with this email",
            });
        }
        // Create tutor
        const tutor = new User_1.User({
            email,
            password,
            profile,
            role: types_1.UserRole.TUTOR,
            isEmailVerified: true,
        });
        await tutor.save();
        logger_1.logger.info(`Tutor created by admin: ${email}`);
        res.status(201).json({
            success: true,
            data: tutor,
            message: "Tutor created successfully",
        });
    }
    catch (error) {
        logger_1.logger.error("Create tutor error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.createTutor = createTutor;
const updateUserStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { isActive } = req.body;
        const user = await User_1.User.findByIdAndUpdate(id, { isActive }, { new: true }).select("-password");
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }
        res.json({
            success: true,
            data: user,
            message: `User ${isActive ? "activated" : "deactivated"} successfully`,
        });
    }
    catch (error) {
        logger_1.logger.error("Update user status error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.updateUserStatus = updateUserStatus;
//# sourceMappingURL=userController.js.map