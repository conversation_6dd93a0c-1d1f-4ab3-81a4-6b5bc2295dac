"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.xenditService = exports.XenditService = void 0;
const axios_1 = __importDefault(require("axios"));
const crypto_1 = __importDefault(require("crypto"));
const logger_1 = require("../utils/logger");
class XenditService {
    constructor() {
        this.apiKey = process.env.XENDIT_SECRET_KEY || '';
        this.webhookToken = process.env.XENDIT_WEBHOOK_TOKEN || '';
        this.baseUrl = 'https://api.xendit.co';
        if (!this.apiKey) {
            throw new Error('Xendit API key is required');
        }
    }
    /**
     * Create payment invoice
     */
    async createInvoice(externalId, amount, description, customerName, customerEmail, duration = 86400 // 24 hours in seconds
    ) {
        try {
            const invoiceData = {
                external_id: externalId,
                amount: amount,
                description: description,
                invoice_duration: duration,
                customer: {
                    given_names: customerName,
                    email: customerEmail
                },
                customer_notification_preference: {
                    invoice_created: ['email'],
                    invoice_reminder: ['email'],
                    invoice_paid: ['email']
                },
                success_redirect_url: `${process.env.FRONTEND_URL}/payment/success`,
                failure_redirect_url: `${process.env.FRONTEND_URL}/payment/failed`
            };
            const response = await axios_1.default.post(`${this.baseUrl}/v2/invoices`, invoiceData, {
                headers: {
                    'Authorization': `Basic ${Buffer.from(this.apiKey + ':').toString('base64')}`,
                    'Content-Type': 'application/json'
                }
            });
            logger_1.logger.info(`Invoice created: ${response.data.id} for external_id: ${externalId}`);
            return response.data;
        }
        catch (error) {
            logger_1.logger.error('Error creating Xendit invoice:', error.response?.data || error.message);
            throw new Error('Failed to create payment invoice');
        }
    }
    /**
     * Get invoice by ID
     */
    async getInvoice(invoiceId) {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/v2/invoices/${invoiceId}`, {
                headers: {
                    'Authorization': `Basic ${Buffer.from(this.apiKey + ':').toString('base64')}`
                }
            });
            return response.data;
        }
        catch (error) {
            logger_1.logger.error('Error getting Xendit invoice:', error.response?.data || error.message);
            throw new Error('Failed to get invoice');
        }
    }
    /**
     * Verify webhook signature
     */
    verifyWebhookSignature(rawBody, signature) {
        try {
            const computedSignature = crypto_1.default
                .createHmac('sha256', this.webhookToken)
                .update(rawBody)
                .digest('hex');
            return signature === computedSignature;
        }
        catch (error) {
            logger_1.logger.error('Error verifying webhook signature:', error);
            return false;
        }
    }
    /**
     * Process webhook payload
     */
    processWebhook(payload) {
        return {
            event: payload.event || 'invoice.paid',
            invoiceId: payload.id,
            externalId: payload.external_id,
            status: payload.status,
            amount: payload.amount,
            paidAt: payload.paid_at ? new Date(payload.paid_at) : undefined
        };
    }
}
exports.XenditService = XenditService;
exports.xenditService = new XenditService();
//# sourceMappingURL=xenditService.js.map