"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMyCourses = exports.publishCourse = exports.deleteCourse = exports.updateCourse = exports.getCourseById = exports.getAllCourses = exports.createCourse = void 0;
const Course_1 = require("../models/Course");
const Module_1 = require("../models/Module");
const Lesson_1 = require("../models/Lesson");
const types_1 = require("../shared/types");
const logger_1 = require("../utils/logger");
const createCourse = async (req, res) => {
    try {
        const { title, description, category, duration, price, currency, tags, requirements, learningOutcomes } = req.body;
        const tutorId = req.user.id;
        // Only tutors and admins can create courses
        if (req.user.role !== types_1.UserRole.TUTOR && req.user.role !== types_1.UserRole.ADMIN) {
            return res.status(403).json({
                success: false,
                message: 'Only tutors and admins can create courses'
            });
        }
        const course = new Course_1.Course({
            title,
            description,
            category,
            tutorId,
            duration,
            totalModules: 0, // Will be updated when modules are added
            price,
            currency: currency || 'USD',
            tags: tags || [],
            requirements: requirements || [],
            learningOutcomes: learningOutcomes || [],
            status: types_1.CourseStatus.DRAFT
        });
        await course.save();
        logger_1.logger.info(`Course created: ${course.title} by tutor ${tutorId}`);
        res.status(201).json({
            success: true,
            data: course,
            message: 'Course created successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error creating course:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.createCourse = createCourse;
const getAllCourses = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const category = req.query.category;
        const status = req.query.status;
        const search = req.query.search;
        const filter = {};
        if (category) {
            filter.category = category;
        }
        if (status) {
            filter.status = status;
        }
        else {
            // Default to published courses for public access
            filter.status = types_1.CourseStatus.PUBLISHED;
        }
        if (search) {
            filter.$text = { $search: search };
        }
        const courses = await Course_1.Course.find(filter)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ createdAt: -1 });
        const total = await Course_1.Course.countDocuments(filter);
        res.json({
            success: true,
            data: {
                courses,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting courses:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getAllCourses = getAllCourses;
const getCourseById = async (req, res) => {
    try {
        const { id } = req.params;
        const course = await Course_1.Course.findById(id);
        if (!course) {
            return res.status(404).json({
                success: false,
                message: 'Course not found'
            });
        }
        // Get course modules and lessons
        const modules = await Module_1.Module.find({ courseId: id }).sort({ order: 1 });
        const moduleIds = modules.map(m => m.id);
        const lessons = await Lesson_1.Lesson.find({ moduleId: { $in: moduleIds } }).sort({ order: 1 });
        const courseWithContent = {
            ...course.toObject(),
            modules: modules.map(module => ({
                ...module.toObject(),
                lessons: lessons.filter(lesson => lesson.moduleId === module.id)
            }))
        };
        res.json({
            success: true,
            data: courseWithContent
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting course by ID:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getCourseById = getCourseById;
const updateCourse = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;
        const course = await Course_1.Course.findById(id);
        if (!course) {
            return res.status(404).json({
                success: false,
                message: 'Course not found'
            });
        }
        // Check if user can update this course
        if (req.user.role !== types_1.UserRole.ADMIN && course.tutorId !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'You can only update your own courses'
            });
        }
        const updatedCourse = await Course_1.Course.findByIdAndUpdate(id, { $set: updates }, { new: true, runValidators: true });
        res.json({
            success: true,
            data: updatedCourse,
            message: 'Course updated successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error updating course:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.updateCourse = updateCourse;
const deleteCourse = async (req, res) => {
    try {
        const { id } = req.params;
        const course = await Course_1.Course.findById(id);
        if (!course) {
            return res.status(404).json({
                success: false,
                message: 'Course not found'
            });
        }
        // Check if user can delete this course
        if (req.user.role !== types_1.UserRole.ADMIN && course.tutorId !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'You can only delete your own courses'
            });
        }
        // Delete associated modules and lessons
        const modules = await Module_1.Module.find({ courseId: id });
        const moduleIds = modules.map(m => m.id);
        await Lesson_1.Lesson.deleteMany({ moduleId: { $in: moduleIds } });
        await Module_1.Module.deleteMany({ courseId: id });
        await Course_1.Course.findByIdAndDelete(id);
        res.json({
            success: true,
            message: 'Course deleted successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error deleting course:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.deleteCourse = deleteCourse;
const publishCourse = async (req, res) => {
    try {
        const { id } = req.params;
        const course = await Course_1.Course.findById(id);
        if (!course) {
            return res.status(404).json({
                success: false,
                message: 'Course not found'
            });
        }
        // Check if user can publish this course
        if (req.user.role !== types_1.UserRole.ADMIN && course.tutorId !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'You can only publish your own courses'
            });
        }
        // Validate course has content before publishing
        const moduleCount = await Module_1.Module.countDocuments({ courseId: id });
        if (moduleCount === 0) {
            return res.status(400).json({
                success: false,
                message: 'Course must have at least one module before publishing'
            });
        }
        course.status = types_1.CourseStatus.PUBLISHED;
        course.totalModules = moduleCount;
        await course.save();
        res.json({
            success: true,
            data: course,
            message: 'Course published successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error publishing course:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.publishCourse = publishCourse;
const getMyCourses = async (req, res) => {
    try {
        const tutorId = req.user.id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const courses = await Course_1.Course.find({ tutorId })
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ createdAt: -1 });
        const total = await Course_1.Course.countDocuments({ tutorId });
        res.json({
            success: true,
            data: {
                courses,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting my courses:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getMyCourses = getMyCourses;
//# sourceMappingURL=courseController.js.map