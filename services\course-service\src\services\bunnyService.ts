import axios from 'axios';
import { logger } from '../utils/logger';

interface BunnyVideoUploadResponse {
  guid: string;
  videoLibraryId: number;
  title: string;
  dateUploaded: string;
  views: number;
  isPublic: boolean;
  length: number;
  status: number;
  frameworkUrl: string;
  thumbnailUrl: string;
  mp4Url: string;
}

interface BunnyStreamingUrls {
  hlsUrl: string;
  dashUrl: string;
  mp4Url: string;
  thumbnailUrl: string;
}

export class BunnyService {
  private apiKey: string;
  private storageZone: string;
  private videoLibraryId: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.BUNNY_API_KEY || '';
    this.storageZone = process.env.BUNNY_STORAGE_ZONE || '';
    this.videoLibraryId = process.env.BUNNY_VIDEO_LIBRARY_ID || '';
    this.baseUrl = 'https://video.bunnycdn.com';

    if (!this.apiKey || !this.storageZone) {
      throw new Error('Bunny.net configuration is missing');
    }
  }

  /**
   * Upload video to Bunny.net
   */
  async uploadVideo(videoBuffer: Buffer, title: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/library/${this.videoLibraryId}/videos`,
        {
          title: title
        },
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      const videoId = response.data.guid;

      // Upload the actual video file
      await axios.put(
        `${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}`,
        videoBuffer,
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/octet-stream'
          }
        }
      );

      logger.info(`Video uploaded successfully: ${videoId}`);
      return videoId;
    } catch (error) {
      logger.error('Error uploading video to Bunny.net:', error);
      throw new Error('Failed to upload video');
    }
  }

  /**
   * Get streaming URLs for a video
   */
  async getStreamingUrls(videoId: string): Promise<BunnyStreamingUrls> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}`,
        {
          headers: {
            'AccessKey': this.apiKey
          }
        }
      );

      const video = response.data;
      
      return {
        hlsUrl: `https://iframe.mediadelivery.net/embed/${this.videoLibraryId}/${videoId}`,
        dashUrl: `https://video.bunnycdn.com/play/${this.videoLibraryId}/${videoId}`,
        mp4Url: video.mp4Url || '',
        thumbnailUrl: video.thumbnailUrl || ''
      };
    } catch (error) {
      logger.error('Error getting streaming URLs:', error);
      throw new Error('Failed to get streaming URLs');
    }
  }

  /**
   * Generate secure video token for authenticated access
   */
  generateSecureToken(videoId: string, userId: string, expirationTime?: number): string {
    const expiry = expirationTime || Math.floor(Date.now() / 1000) + (24 * 60 * 60); // 24 hours
    const tokenData = {
      videoId,
      userId,
      exp: expiry
    };

    // In production, use proper JWT signing
    return Buffer.from(JSON.stringify(tokenData)).toString('base64');
  }

  /**
   * Delete video from Bunny.net
   */
  async deleteVideo(videoId: string): Promise<boolean> {
    try {
      await axios.delete(
        `${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}`,
        {
          headers: {
            'AccessKey': this.apiKey
          }
        }
      );

      logger.info(`Video deleted successfully: ${videoId}`);
      return true;
    } catch (error) {
      logger.error('Error deleting video:', error);
      return false;
    }
  }

  /**
   * Get video analytics
   */
  async getVideoAnalytics(videoId: string): Promise<any> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/library/${this.videoLibraryId}/videos/${videoId}/statistics`,
        {
          headers: {
            'AccessKey': this.apiKey
          }
        }
      );

      return response.data;
    } catch (error) {
      logger.error('Error getting video analytics:', error);
      throw new Error('Failed to get video analytics');
    }
  }
}

export const bunnyService = new BunnyService();
