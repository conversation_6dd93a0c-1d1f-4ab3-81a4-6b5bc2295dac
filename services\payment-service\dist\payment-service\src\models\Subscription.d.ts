import mongoose, { Document } from "mongoose";
import { Subscription as ISubscription } from "../shared/types";
export interface SubscriptionDocument extends ISubscription, Document {
}
export declare const Subscription: mongoose.Model<SubscriptionDocument, {}, {}, {}, mongoose.Document<unknown, {}, SubscriptionDocument, {}, {}> & SubscriptionDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Subscription.d.ts.map