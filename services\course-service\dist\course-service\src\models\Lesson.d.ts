import mongoose, { Document } from "mongoose";
import { Lesson as ILesson } from "../shared/types";
export interface LessonDocument extends ILesson, Document {
}
export declare const Lesson: mongoose.Model<LessonDocument, {}, {}, {}, mongoose.Document<unknown, {}, LessonDocument, {}, {}> & LessonDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Lesson.d.ts.map