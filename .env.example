# Environment Configuration for Time Course Platform

# Application Environment
NODE_ENV=development

# Firebase Configuration (Required for Google OAuth)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Xendit Payment Configuration
XENDIT_SECRET_KEY=your_xendit_secret_key
XENDIT_WEBHOOK_TOKEN=your_xendit_webhook_token
XENDIT_CALLBACK_URL=http://localhost/api/payments/webhook

# Bunny.net Video Configuration
BUNNY_API_KEY=your_bunny_api_key
BUNNY_STORAGE_ZONE=your_bunny_storage_zone
BUNNY_CDN_URL=https://your-zone.b-cdn.net

# Database URLs (Auto-configured in Docker Compose)
AUTH_MONGODB_URI=mongodb://mongo-auth:27017/time_course_auth
COURSE_MONGODB_URI=mongodb://mongo-course:27017/time_course_courses
PAYMENT_MONGODB_URI=mongodb://mongo-payment:27017/time_course_payments
ANALYTICS_MONGODB_URI=mongodb://mongo-analytics:27017/time_course_analytics

# Service URLs (Auto-configured in Docker Compose)
AUTH_SERVICE_URL=http://auth-service:3001
COURSE_SERVICE_URL=http://course-service:3002
PAYMENT_SERVICE_URL=http://payment-service:3003
ANALYTICS_SERVICE_URL=http://analytics-service:3004

# Email Configuration (Future implementation)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# File Upload Configuration
MAX_FILE_SIZE=100MB
UPLOAD_PATH=/tmp/uploads

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=/app/logs