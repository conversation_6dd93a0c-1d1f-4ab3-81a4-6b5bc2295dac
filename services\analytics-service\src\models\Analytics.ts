import mongoose, { Document, Schema } from 'mongoose';

// Platform Analytics Model
export interface PlatformAnalyticsDocument extends Document {
  date: Date;
  totalUsers: number;
  activeSubscriptions: number;
  totalRevenue: number;
  newRegistrations: number;
  courseCompletions: number;
  assignmentFailures: number;
  terminatedSubscriptions: number;
  coachingSessionsCreated: number;
  createdAt: Date;
  updatedAt: Date;
}

const platformAnalyticsSchema = new Schema<PlatformAnalyticsDocument>({
  date: { type: Date, required: true, unique: true },
  totalUsers: { type: Number, default: 0 },
  activeSubscriptions: { type: Number, default: 0 },
  totalRevenue: { type: Number, default: 0 },
  newRegistrations: { type: Number, default: 0 },
  courseCompletions: { type: Number, default: 0 },
  assignmentFailures: { type: Number, default: 0 },
  terminatedSubscriptions: { type: Number, default: 0 },
  coachingSessionsCreated: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Course Analytics Model
export interface CourseAnalyticsDocument extends Document {
  courseId: string;
  tutorId: string;
  title: string;
  category: string;
  totalEnrollments: number;
  activeEnrollments: number;
  completionRate: number;
  averageProgress: number;
  totalRevenue: number;
  assignmentFailureRate: number;
  lastUpdated: Date;
}

const courseAnalyticsSchema = new Schema<CourseAnalyticsDocument>({
  courseId: { type: String, required: true, unique: true },
  tutorId: { type: String, required: true },
  title: { type: String, required: true },
  category: { type: String, required: true },
  totalEnrollments: { type: Number, default: 0 },
  activeEnrollments: { type: Number, default: 0 },
  completionRate: { type: Number, default: 0 },
  averageProgress: { type: Number, default: 0 },
  totalRevenue: { type: Number, default: 0 },
  assignmentFailureRate: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
});

// User Analytics Model
export interface UserAnalyticsDocument extends Document {
  userId: string;
  role: string;
  totalCourses: number;
  completedCourses: number;
  activeSubscriptions: number;
  totalSpent: number;
  averageProgress: number;
  assignmentFailures: number;
  lastActivity: Date;
  joinedDate: Date;
}

const userAnalyticsSchema = new Schema<UserAnalyticsDocument>({
  userId: { type: String, required: true, unique: true },
  role: { type: String, required: true },
  totalCourses: { type: Number, default: 0 },
  completedCourses: { type: Number, default: 0 },
  activeSubscriptions: { type: Number, default: 0 },
  totalSpent: { type: Number, default: 0 },
  averageProgress: { type: Number, default: 0 },
  assignmentFailures: { type: Number, default: 0 },
  lastActivity: { type: Date, default: Date.now },
  joinedDate: { type: Date, default: Date.now }
});

// Indexes for better performance
platformAnalyticsSchema.index({ date: -1 });
courseAnalyticsSchema.index({ tutorId: 1 });
courseAnalyticsSchema.index({ category: 1 });
userAnalyticsSchema.index({ role: 1 });
userAnalyticsSchema.index({ lastActivity: -1 });

export const PlatformAnalytics = mongoose.model<PlatformAnalyticsDocument>('PlatformAnalytics', platformAnalyticsSchema);
export const CourseAnalytics = mongoose.model<CourseAnalyticsDocument>('CourseAnalytics', courseAnalyticsSchema);
export const UserAnalytics = mongoose.model<UserAnalyticsDocument>('UserAnalytics', userAnalyticsSchema);
