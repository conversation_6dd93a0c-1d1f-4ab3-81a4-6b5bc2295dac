"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const assignmentController_1 = require("../controllers/assignmentController");
const router = express_1.default.Router();
// Get assignments by lesson
router.get('/lesson/:lessonId', assignmentController_1.getAssignmentsByLesson);
// Get assignment by ID
router.get('/:id', assignmentController_1.getAssignmentById);
// Create assignment (tutor/admin only)
router.post('/', [
    auth_1.auth,
    (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN),
    (0, express_validator_1.body)('lessonId').notEmpty().withMessage('Lesson ID is required'),
    (0, express_validator_1.body)('title').notEmpty().withMessage('Assignment title is required'),
    (0, express_validator_1.body)('questions').isArray({ min: 1 }).withMessage('At least one question is required'),
    (0, express_validator_1.body)('triggerTime').isInt({ min: 0 }).withMessage('Trigger time must be a positive integer'),
    (0, express_validator_1.body)('timeLimit').isInt({ min: 1 }).withMessage('Time limit must be a positive integer'),
    validation_1.validate
], assignmentController_1.createAssignment);
// Submit assignment (student only)
router.post('/:id/submit', [
    auth_1.auth,
    (0, auth_1.authorize)(types_1.UserRole.STUDENT),
    (0, express_validator_1.body)('answers').isArray().withMessage('Answers must be an array'),
    (0, express_validator_1.body)('subscriptionId').notEmpty().withMessage('Subscription ID is required'),
    validation_1.validate
], assignmentController_1.submitAssignment);
// Get assignment submissions (student only)
router.get('/:id/submissions', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.STUDENT), assignmentController_1.getAssignmentSubmissions);
// Update assignment (tutor/admin only)
router.put('/:id', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), assignmentController_1.updateAssignment);
// Delete assignment (tutor/admin only)
router.delete('/:id', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), assignmentController_1.deleteAssignment);
exports.default = router;
//# sourceMappingURL=assignment.js.map