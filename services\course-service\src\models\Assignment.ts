import mongoose, { Document, Schema } from "mongoose";
import {
  Assignment as IAssignment,
  Question,
  QuestionType,
} from "../shared/types";

export interface AssignmentDocument extends IAssignment, Document {}

const questionSchema = new Schema<Question>({
  text: { type: String, required: true },
  type: {
    type: String,
    enum: Object.values(QuestionType),
    required: true,
  },
  options: [{ type: String }], // for multiple choice
  correctAnswer: { type: String, required: true },
  points: { type: Number, required: true, min: 1 },
  order: { type: Number, required: true },
});

const assignmentSchema = new Schema<AssignmentDocument>(
  {
    lessonId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    questions: [questionSchema],
    triggerTime: { type: Number, required: true }, // timestamp in video (seconds)
    timeLimit: { type: Number, required: true }, // in minutes
    passingScore: { type: Number, required: true, min: 0, max: 100 }, // percentage
    maxAttempts: { type: Number, default: 1 },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
  }
);

// Index for lesson and trigger time
assignmentSchema.index({ lessonId: 1, triggerTime: 1 });

export const Assignment = mongoose.model<AssignmentDocument>(
  "Assignment",
  assignmentSchema
);
