export declare class SubscriptionService {
    /**
     * Create subscription after successful payment
     */
    createSubscription(studentId: string, courseId: string, paymentId: string, courseDuration: number, amount: number, currency?: string): Promise<any>;
    /**
     * Record assignment failure and check termination
     */
    recordAssignmentFailure(subscriptionId: string): Promise<{
        subscription: any;
        isTerminated: boolean;
        failureCount: number;
    }>;
    /**
     * Get active subscription for student and course
     */
    getActiveSubscription(studentId: string, courseId: string): Promise<any>;
    /**
     * Get subscriptions eligible for coaching
     */
    getCoachingEligibleSubscriptions(courseId?: string): Promise<any[]>;
    /**
     * Check and expire subscriptions
     */
    checkExpiredSubscriptions(): Promise<void>;
    /**
     * Initialize progress tracking in course service
     */
    private initializeProgressTracking;
}
export declare const subscriptionService: SubscriptionService;
//# sourceMappingURL=subscriptionService.d.ts.map