{"name": "time-mongo-microservices", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean": "rimraf services/analytics-service/dist services/auth-service/dist services/course-service/dist services/payment-service/dist", "clean:windows": "if exist services\\analytics-service\\dist rmdir /s /q services\\analytics-service\\dist && if exist services\\auth-service\\dist rmdir /s /q services\\auth-service\\dist && if exist services\\course-service\\dist rmdir /s /q services\\course-service\\dist && if exist services\\payment-service\\dist rmdir /s /q services\\payment-service\\dist", "build:analytics": "cd services/analytics-service && npm run build", "build:auth": "cd services/auth-service && npm run build", "build:course": "cd services/course-service && npm run build", "build:payment": "cd services/payment-service && npm run build", "build:all": "npm run build:analytics && npm run build:auth && npm run build:course && npm run build:payment", "rebuild": "npm run clean && npm run build:all", "rebuild:windows": "npm run clean:windows && npm run build:all", "start:all": "docker-compose up --build", "stop:all": "docker-compose down", "dev:analytics": "cd services/analytics-service && npm run dev", "dev:auth": "cd services/auth-service && npm run dev", "dev:course": "cd services/course-service && npm run dev", "dev:payment": "cd services/payment-service && npm run dev"}, "devDependencies": {"rimraf": "^5.0.5"}}