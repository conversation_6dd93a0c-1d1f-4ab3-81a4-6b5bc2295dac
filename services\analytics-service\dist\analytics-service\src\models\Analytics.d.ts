import mongoose, { Document } from 'mongoose';
export interface PlatformAnalyticsDocument extends Document {
    date: Date;
    totalUsers: number;
    activeSubscriptions: number;
    totalRevenue: number;
    newRegistrations: number;
    courseCompletions: number;
    assignmentFailures: number;
    terminatedSubscriptions: number;
    coachingSessionsCreated: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface CourseAnalyticsDocument extends Document {
    courseId: string;
    tutorId: string;
    title: string;
    category: string;
    totalEnrollments: number;
    activeEnrollments: number;
    completionRate: number;
    averageProgress: number;
    totalRevenue: number;
    assignmentFailureRate: number;
    lastUpdated: Date;
}
export interface UserAnalyticsDocument extends Document {
    userId: string;
    role: string;
    totalCourses: number;
    completedCourses: number;
    activeSubscriptions: number;
    totalSpent: number;
    averageProgress: number;
    assignmentFailures: number;
    lastActivity: Date;
    joinedDate: Date;
}
export declare const PlatformAnalytics: mongoose.Model<PlatformAnalyticsDocument, {}, {}, {}, mongoose.Document<unknown, {}, PlatformAnalyticsDocument, {}, {}> & PlatformAnalyticsDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export declare const CourseAnalytics: mongoose.Model<CourseAnalyticsDocument, {}, {}, {}, mongoose.Document<unknown, {}, CourseAnalyticsDocument, {}, {}> & CourseAnalyticsDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export declare const UserAnalytics: mongoose.Model<UserAnalyticsDocument, {}, {}, {}, mongoose.Document<unknown, {}, UserAnalyticsDocument, {}, {}> & UserAnalyticsDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Analytics.d.ts.map