"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllSubscriptions = exports.getCoachingEligible = exports.recordFailure = exports.getSubscriptionById = exports.getMySubscriptions = void 0;
const Subscription_1 = require("../models/Subscription");
const subscriptionService_1 = require("../services/subscriptionService");
const types_1 = require("../shared/types");
const logger_1 = require("../utils/logger");
const getMySubscriptions = async (req, res) => {
    try {
        const studentId = req.user.id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const subscriptions = await Subscription_1.Subscription.find({ studentId })
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ createdAt: -1 });
        const total = await Subscription_1.Subscription.countDocuments({ studentId });
        res.json({
            success: true,
            data: {
                subscriptions,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting my subscriptions:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getMySubscriptions = getMySubscriptions;
const getSubscriptionById = async (req, res) => {
    try {
        const { id } = req.params;
        const studentId = req.user.id;
        const subscription = await Subscription_1.Subscription.findOne({ _id: id, studentId });
        if (!subscription) {
            return res.status(404).json({
                success: false,
                message: 'Subscription not found'
            });
        }
        res.json({
            success: true,
            data: subscription
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting subscription by ID:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getSubscriptionById = getSubscriptionById;
const recordFailure = async (req, res) => {
    try {
        const { subscriptionId } = req.body;
        const result = await subscriptionService_1.subscriptionService.recordAssignmentFailure(subscriptionId);
        res.json({
            success: true,
            data: result,
            message: result.isTerminated ?
                'Subscription terminated due to excessive failures' :
                'Failure recorded successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error recording failure:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.recordFailure = recordFailure;
const getCoachingEligible = async (req, res) => {
    try {
        const { courseId } = req.query;
        // Only tutors and admins can view coaching eligible students
        if (req.user.role !== types_1.UserRole.TUTOR && req.user.role !== types_1.UserRole.ADMIN) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }
        const subscriptions = await subscriptionService_1.subscriptionService.getCoachingEligibleSubscriptions(courseId);
        res.json({
            success: true,
            data: subscriptions
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting coaching eligible subscriptions:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getCoachingEligible = getCoachingEligible;
const getAllSubscriptions = async (req, res) => {
    try {
        // Only admins can view all subscriptions
        if (req.user.role !== types_1.UserRole.ADMIN) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const filter = {};
        if (status) {
            filter.status = status;
        }
        const subscriptions = await Subscription_1.Subscription.find(filter)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ createdAt: -1 });
        const total = await Subscription_1.Subscription.countDocuments(filter);
        res.json({
            success: true,
            data: {
                subscriptions,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting all subscriptions:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getAllSubscriptions = getAllSubscriptions;
//# sourceMappingURL=subscriptionController.js.map