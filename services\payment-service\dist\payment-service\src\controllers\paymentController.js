"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMyPayments = exports.getPaymentStatus = exports.createPayment = void 0;
const Payment_1 = require("../models/Payment");
const types_1 = require("../shared/types");
const xenditService_1 = require("../services/xenditService");
const subscriptionService_1 = require("../services/subscriptionService");
const logger_1 = require("../utils/logger");
const axios_1 = __importDefault(require("axios"));
const createPayment = async (req, res) => {
    try {
        const { courseId, amount, courseDuration } = req.body;
        const studentId = req.user.id;
        const studentEmail = req.user.email;
        const studentName = `${req.user.profile.firstName} ${req.user.profile.lastName}`;
        // Check if student already has active subscription for this course
        const existingSubscription = await subscriptionService_1.subscriptionService.getActiveSubscription(studentId, courseId);
        if (existingSubscription) {
            return res.status(400).json({
                success: false,
                message: "You already have an active subscription for this course",
            });
        }
        // Get course details
        const courseServiceUrl = process.env.COURSE_SERVICE_URL || "http://localhost:3002";
        let courseTitle = "Course";
        try {
            const courseResponse = await axios_1.default.get(`${courseServiceUrl}/api/courses/${courseId}`);
            courseTitle = courseResponse.data.data.title;
        }
        catch (error) {
            logger_1.logger.warn("Could not fetch course details:", error);
        }
        // Generate unique external ID
        const externalId = `TC-${Date.now()}-${studentId.slice(-6)}`;
        // Create payment record
        const payment = new Payment_1.Payment({
            studentId,
            courseId,
            amount,
            currency: "IDR",
            status: types_1.PaymentStatus.PENDING,
            paymentMethod: "xendit_invoice",
            externalId,
            expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        });
        await payment.save();
        // Create Xendit invoice
        const invoice = await xenditService_1.xenditService.createInvoice(externalId, amount, `Time Course - ${courseTitle} (${courseDuration} days)`, studentName, studentEmail);
        // Update payment with Xendit details
        payment.invoiceId = invoice.id;
        payment.paymentUrl = invoice.invoice_url;
        payment.expiryDate = new Date(invoice.expiry_date);
        await payment.save();
        logger_1.logger.info(`Payment created: ${payment.id} for course ${courseId}`);
        res.status(201).json({
            success: true,
            data: {
                paymentId: payment.id,
                invoiceId: invoice.id,
                paymentUrl: invoice.invoice_url,
                amount: invoice.amount,
                expiryDate: invoice.expiry_date,
                externalId: externalId,
            },
            message: "Payment created successfully",
        });
    }
    catch (error) {
        logger_1.logger.error("Error creating payment:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.createPayment = createPayment;
const getPaymentStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const studentId = req.user.id;
        const payment = await Payment_1.Payment.findOne({ _id: id, studentId });
        if (!payment) {
            return res.status(404).json({
                success: false,
                message: "Payment not found",
            });
        }
        // If payment is still pending, check with Xendit
        if (payment.status === types_1.PaymentStatus.PENDING && payment.invoiceId) {
            try {
                const invoice = await xenditService_1.xenditService.getInvoice(payment.invoiceId);
                if (invoice.status === "PAID") {
                    // Update payment status
                    payment.status = types_1.PaymentStatus.COMPLETED;
                    payment.paidAt = new Date(invoice.updated);
                    payment.transactionId = invoice.id;
                    await payment.save();
                    // Create subscription
                    await subscriptionService_1.subscriptionService.createSubscription(payment.studentId, payment.courseId, payment.id, 90, // Default 90 days - should come from course data
                    payment.amount, payment.currency);
                }
            }
            catch (error) {
                logger_1.logger.error("Error checking payment status with Xendit:", error);
            }
        }
        res.json({
            success: true,
            data: payment,
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting payment status:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.getPaymentStatus = getPaymentStatus;
const getMyPayments = async (req, res) => {
    try {
        const studentId = req.user.id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const payments = await Payment_1.Payment.find({ studentId })
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .sort({ createdAt: -1 });
        const total = await Payment_1.Payment.countDocuments({ studentId });
        res.json({
            success: true,
            data: {
                payments,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                },
            },
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting my payments:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.getMyPayments = getMyPayments;
//# sourceMappingURL=paymentController.js.map