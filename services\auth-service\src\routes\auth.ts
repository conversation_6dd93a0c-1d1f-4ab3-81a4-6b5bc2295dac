import express from 'express';
import { body } from 'express-validator';
import { register, login, googleLogin, logout, refreshToken, verifyEmail } from '../controllers/authController';
import { auth } from '../middleware/auth';
import { validate } from '../middleware/validation';

const router = express.Router();

// Registration
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('profile.firstName').notEmpty().withMessage('First name is required'),
  body('profile.lastName').notEmpty().withMessage('Last name is required'),
  validate
], register);

// Login
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty().withMessage('Password is required'),
  validate
], login);

// Google Login
router.post('/google', [
  body('idToken').notEmpty().withMessage('Firebase ID token is required'),
  validate
], googleLogin);

// Logout
router.post('/logout', auth, logout);

// Refresh Token
router.post('/refresh', refreshToken);

// Verify Email
router.get('/verify/:token', verifyEmail);

// Get current user
router.get('/me', auth, (req: any, res) => {
  res.json({
    success: true,
    data: req.user
  });
});

export default router;