import mongoose, { Document } from "mongoose";
import { User as IUser } from "../shared/types";
export interface UserDocument extends IUser, Document {
    password?: string;
    firebaseUid?: string;
    comparePassword(candidatePassword: string): Promise<boolean>;
}
export declare const User: mongoose.Model<UserDocument, {}, {}, {}, mongoose.Document<unknown, {}, UserDocument, {}, {}> & UserDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=User.d.ts.map