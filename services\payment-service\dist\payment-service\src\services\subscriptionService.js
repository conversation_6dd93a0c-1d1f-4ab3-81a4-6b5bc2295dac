"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.subscriptionService = exports.SubscriptionService = void 0;
const Subscription_1 = require("../models/Subscription");
const types_1 = require("../shared/types");
const logger_1 = require("../utils/logger");
const axios_1 = __importDefault(require("axios"));
class SubscriptionService {
    /**
     * Create subscription after successful payment
     */
    async createSubscription(studentId, courseId, paymentId, courseDuration, amount, currency = 'IDR') {
        try {
            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(startDate.getDate() + courseDuration);
            const subscription = new Subscription_1.Subscription({
                studentId,
                courseId,
                paymentId,
                amount,
                currency,
                startDate,
                endDate,
                status: types_1.SubscriptionStatus.ACTIVE,
                failureCount: 0
            });
            await subscription.save();
            // Initialize progress tracking in course service
            await this.initializeProgressTracking(studentId, courseId, subscription.id);
            logger_1.logger.info(`Subscription created: ${subscription.id} for student ${studentId}`);
            return subscription;
        }
        catch (error) {
            logger_1.logger.error('Error creating subscription:', error);
            throw error;
        }
    }
    /**
     * Record assignment failure and check termination
     */
    async recordAssignmentFailure(subscriptionId) {
        try {
            const subscription = await Subscription_1.Subscription.findById(subscriptionId);
            if (!subscription) {
                throw new Error('Subscription not found');
            }
            // Increment failure count
            subscription.failureCount += 1;
            // Check if termination threshold is reached
            if (subscription.failureCount >= subscription.maxFailures) {
                subscription.status = types_1.SubscriptionStatus.TERMINATED;
                subscription.isEligibleForCoaching = true;
                subscription.terminatedAt = new Date();
                subscription.terminationReason = `Exceeded maximum failures (${subscription.maxFailures})`;
                logger_1.logger.warn(`Subscription terminated: ${subscriptionId} due to ${subscription.failureCount} failures`);
            }
            await subscription.save();
            return {
                subscription,
                isTerminated: subscription.status === types_1.SubscriptionStatus.TERMINATED,
                failureCount: subscription.failureCount
            };
        }
        catch (error) {
            logger_1.logger.error('Error recording assignment failure:', error);
            throw error;
        }
    }
    /**
     * Get active subscription for student and course
     */
    async getActiveSubscription(studentId, courseId) {
        try {
            const subscription = await Subscription_1.Subscription.findOne({
                studentId,
                courseId,
                status: types_1.SubscriptionStatus.ACTIVE,
                endDate: { $gt: new Date() }
            });
            return subscription;
        }
        catch (error) {
            logger_1.logger.error('Error getting active subscription:', error);
            throw error;
        }
    }
    /**
     * Get subscriptions eligible for coaching
     */
    async getCoachingEligibleSubscriptions(courseId) {
        try {
            const filter = {
                status: types_1.SubscriptionStatus.TERMINATED,
                isEligibleForCoaching: true
            };
            if (courseId) {
                filter.courseId = courseId;
            }
            const subscriptions = await Subscription_1.Subscription.find(filter)
                .sort({ terminatedAt: -1 });
            return subscriptions;
        }
        catch (error) {
            logger_1.logger.error('Error getting coaching eligible subscriptions:', error);
            throw error;
        }
    }
    /**
     * Check and expire subscriptions
     */
    async checkExpiredSubscriptions() {
        try {
            const expiredSubscriptions = await Subscription_1.Subscription.find({
                status: types_1.SubscriptionStatus.ACTIVE,
                endDate: { $lt: new Date() }
            });
            for (const subscription of expiredSubscriptions) {
                subscription.status = types_1.SubscriptionStatus.EXPIRED;
                await subscription.save();
                logger_1.logger.info(`Subscription expired: ${subscription.id}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Error checking expired subscriptions:', error);
        }
    }
    /**
     * Initialize progress tracking in course service
     */
    async initializeProgressTracking(studentId, courseId, subscriptionId) {
        try {
            const courseServiceUrl = process.env.COURSE_SERVICE_URL || 'http://localhost:3002';
            await axios_1.default.post(`${courseServiceUrl}/api/progress/initialize`, {
                studentId,
                courseId,
                subscriptionId
            });
            logger_1.logger.info(`Progress tracking initialized for subscription: ${subscriptionId}`);
        }
        catch (error) {
            logger_1.logger.error('Error initializing progress tracking:', error);
            // Don't throw error as this is not critical for subscription creation
        }
    }
}
exports.SubscriptionService = SubscriptionService;
exports.subscriptionService = new SubscriptionService();
//# sourceMappingURL=subscriptionService.js.map