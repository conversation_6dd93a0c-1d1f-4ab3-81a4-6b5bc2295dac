import mongoose, { Document } from "mongoose";
import { Assignment as IAssignment } from "../shared/types";
export interface AssignmentDocument extends IAssignment, Document {
}
export declare const Assignment: mongoose.Model<AssignmentDocument, {}, {}, {}, mongoose.Document<unknown, {}, AssignmentDocument, {}, {}> & AssignmentDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Assignment.d.ts.map