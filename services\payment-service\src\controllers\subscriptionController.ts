import { Request, Response } from 'express';
import { Subscription } from '../models/Subscription';
import { subscriptionService } from '../services/subscriptionService';
import { UserRole } from '../shared/types';
import { logger } from '../utils/logger';

interface AuthRequest extends Request {
  user?: any;
}

export const getMySubscriptions = async (req: AuthRequest, res: Response) => {
  try {
    const studentId = req.user.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const subscriptions = await Subscription.find({ studentId })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Subscription.countDocuments({ studentId });

    res.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting my subscriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getSubscriptionById = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const studentId = req.user.id;

    const subscription = await Subscription.findOne({ _id: id, studentId });
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found'
      });
    }

    res.json({
      success: true,
      data: subscription
    });
  } catch (error) {
    logger.error('Error getting subscription by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const recordFailure = async (req: Request, res: Response) => {
  try {
    const { subscriptionId } = req.body;

    const result = await subscriptionService.recordAssignmentFailure(subscriptionId);

    res.json({
      success: true,
      data: result,
      message: result.isTerminated ? 
        'Subscription terminated due to excessive failures' : 
        'Failure recorded successfully'
    });
  } catch (error) {
    logger.error('Error recording failure:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getCoachingEligible = async (req: AuthRequest, res: Response) => {
  try {
    const { courseId } = req.query;

    // Only tutors and admins can view coaching eligible students
    if (req.user.role !== UserRole.TUTOR && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const subscriptions = await subscriptionService.getCoachingEligibleSubscriptions(courseId as string);

    res.json({
      success: true,
      data: subscriptions
    });
  } catch (error) {
    logger.error('Error getting coaching eligible subscriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAllSubscriptions = async (req: AuthRequest, res: Response) => {
  try {
    // Only admins can view all subscriptions
    if (req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;

    const filter: any = {};
    if (status) {
      filter.status = status;
    }

    const subscriptions = await Subscription.find(filter)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Subscription.countDocuments(filter);

    res.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting all subscriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
