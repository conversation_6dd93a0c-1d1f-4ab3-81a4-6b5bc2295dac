{"version": 3, "file": "progressService.js", "sourceRoot": "", "sources": ["../../../../src/services/progressService.ts"], "names": [], "mappings": ";;;AAAA,iDAA8C;AAC9C,6CAA0C;AAC1C,6CAA0C;AAC1C,6CAA0C;AAC1C,qDAAkD;AAElD,4CAAyC;AAWzC,MAAa,eAAe;IAC1B;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,QAAgB,EAAE,cAAsB;QAClF,IAAI,CAAC;YACH,qBAAqB;YACrB,MAAM,MAAM,GAAG,MAAM,eAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC;YAED,sCAAsC;YACtC,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAEzC,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,MAAM,uBAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAEzF,yBAAyB;YACzB,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC;gBAC5B,SAAS;gBACT,QAAQ;gBACR,cAAc;gBACd,gBAAgB,EAAE,EAAE;gBACpB,oBAAoB,EAAE,EAAE;gBACxB,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,gBAAgB,EAAE,WAAW,CAAC,MAAM;gBACpC,oBAAoB,EAAE,CAAC;gBACvB,aAAa,EAAE,CAAC;aACjB,CAAC,CAAC;YAEH,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,oCAAoC,SAAS,cAAc,QAAQ,EAAE,CAAC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,QAAgB,EAAE,QAAgB;QACxE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClD,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzC,MAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxB,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,UAAU,QAAQ,0BAA0B,SAAS,EAAE,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,QAAgB,EAAE,YAAoB;QAChF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,mDAAmD;YACnD,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC1D,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxB,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,0BAA0B,SAAS,EAAE,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,QAAgB,EAAE,qBAA2B,EAAE,cAAsB;QAC5G,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,eAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC;YAED,oDAAoD;YACpD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,qBAAqB,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAE1G,yEAAyE;YACzE,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,cAAc,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;YAE1F,uCAAuC;YACvC,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,yBAAyB;YAE5E,MAAM,SAAS,GAAG,cAAc,IAAI,gBAAgB,CAAC;YAErD,MAAM,MAAM,GAAiB;gBAC3B,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,WAAW;gBACX,SAAS,EAAE,cAAc;aAC1B,CAAC;YAEF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,CAAC,cAAc,GAAG,sDAAsD,gBAAgB,8CAA8C,cAAc,GAAG,CAAC;YAChK,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,QAAgB;QAC1D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,eAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAEnE,MAAM,eAAe,GAAG;gBACtB,GAAG,QAAQ,CAAC,QAAQ,EAAE;gBACtB,MAAM,EAAE,MAAM,EAAE,KAAK;gBACrB,OAAO,EAAE,EAAE;aACZ,CAAC;YAEF,+BAA+B;YAC/B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC9E,MAAM,cAAc,GAAG;oBACrB,GAAG,MAAM,CAAC,QAAQ,EAAE;oBACpB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;wBAC9B,GAAG,MAAM,CAAC,QAAQ,EAAE;wBACpB,WAAW,EAAE,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;qBAC3D,CAAC,CAAC;oBACH,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM;oBAChG,YAAY,EAAE,OAAO,CAAC,MAAM;iBAC7B,CAAC;gBAEF,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/C,CAAC;YAED,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CAAC,QAAa;QACpD,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC;QACrE,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC;QAE/F,QAAQ,CAAC,oBAAoB,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErG,mDAAmD;QACnD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,gDAAgD;YAChD,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YACtF,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC3D,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAChD,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAC9C,CAAC,MAAM,CAAC;gBAET,IAAI,iBAAiB,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/D,aAAa,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;gBACnC,CAAC;qBAAM,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBACjC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;oBAC7B,MAAM;gBACR,CAAC;YACH,CAAC;YAED,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACnE,CAAC;QAED,4BAA4B;QAC5B,IAAI,QAAQ,CAAC,oBAAoB,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YACnE,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;CACF;AArND,0CAqNC;AAEY,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC"}