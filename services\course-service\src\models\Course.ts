import mongoose, { Document, Schema } from "mongoose";
import {
  Course as ICourse,
  CourseCategory,
  CourseStatus,
} from "../shared/types";

export interface CourseDocument extends ICourse, Document {}

const courseSchema = new Schema<CourseDocument>(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    category: {
      type: String,
      enum: Object.values(CourseCategory),
      required: true,
    },
    tutorId: { type: String, required: true, index: true },
    duration: { type: Number, required: true }, // in days
    totalModules: { type: Number, required: true },
    price: { type: Number, required: true, min: 0 },
    currency: { type: String, default: "USD" },
    status: {
      type: String,
      enum: Object.values(CourseStatus),
      default: CourseStatus.DRAFT,
    },
    thumbnail: { type: String },
    tags: [{ type: String }],
    requirements: [{ type: String }],
    learningOutcomes: [{ type: String }],
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance
courseSchema.index({ category: 1, status: 1 });
courseSchema.index({ tutorId: 1 });
courseSchema.index({ createdAt: -1 });
courseSchema.index({ title: "text", description: "text" });

export const Course = mongoose.model<CourseDocument>("Course", courseSchema);
