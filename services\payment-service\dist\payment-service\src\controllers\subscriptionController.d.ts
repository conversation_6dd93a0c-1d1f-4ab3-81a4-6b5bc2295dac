import { Request, Response } from 'express';
interface AuthRequest extends Request {
    user?: any;
}
export declare const getMySubscriptions: (req: AuthRequest, res: Response) => Promise<void>;
export declare const getSubscriptionById: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const recordFailure: (req: Request, res: Response) => Promise<void>;
export declare const getCoachingEligible: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAllSubscriptions: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export {};
//# sourceMappingURL=subscriptionController.d.ts.map