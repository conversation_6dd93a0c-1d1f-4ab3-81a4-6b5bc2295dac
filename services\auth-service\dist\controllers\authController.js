"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyEmail = exports.refreshToken = exports.logout = exports.googleLogin = exports.login = exports.register = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = require("../models/User");
const firebase_1 = require("../config/firebase");
const types_1 = require("../shared/types");
const logger_1 = require("../utils/logger");
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";
const register = async (req, res) => {
    try {
        const { email, password, profile, role } = req.body;
        // Check if user already exists
        const existingUser = await User_1.User.findOne({ email });
        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: "User already exists with this email",
            });
        }
        // Only admin can create tutors
        const userRole = role === types_1.UserRole.TUTOR ? types_1.UserRole.STUDENT : role || types_1.UserRole.STUDENT;
        // Create new user
        const user = new User_1.User({
            email,
            password,
            profile,
            role: userRole,
            isEmailVerified: true, // Auto-verify for now
        });
        await user.save();
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ userId: user._id, email: user.email, role: user.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
        logger_1.logger.info(`User registered: ${email}`);
        res.status(201).json({
            success: true,
            data: {
                user,
                token,
            },
            message: "User registered successfully",
        });
    }
    catch (error) {
        logger_1.logger.error("Registration error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.register = register;
const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        // Find user by email
        const user = await User_1.User.findOne({ email });
        if (!user) {
            return res.status(401).json({
                success: false,
                message: "Invalid email or password",
            });
        }
        // Check password
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            return res.status(401).json({
                success: false,
                message: "Invalid email or password",
            });
        }
        // Check if user is active
        if (!user.isActive) {
            return res.status(401).json({
                success: false,
                message: "Account is deactivated",
            });
        }
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ userId: user._id, email: user.email, role: user.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
        logger_1.logger.info(`User logged in: ${email}`);
        res.json({
            success: true,
            data: {
                user,
                token,
            },
            message: "Login successful",
        });
    }
    catch (error) {
        logger_1.logger.error("Login error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.login = login;
const googleLogin = async (req, res) => {
    try {
        const { idToken } = req.body;
        // Verify Firebase token
        const decodedToken = await (0, firebase_1.verifyFirebaseToken)(idToken);
        let user = await User_1.User.findOne({
            $or: [{ firebaseUid: decodedToken.uid }, { email: decodedToken.email }],
        });
        if (!user) {
            // Create new user from Google data
            user = new User_1.User({
                email: decodedToken.email,
                firebaseUid: decodedToken.uid,
                profile: {
                    firstName: decodedToken.name?.split(" ")[0] || "",
                    lastName: decodedToken.name?.split(" ").slice(1).join(" ") || "",
                    avatar: decodedToken.picture,
                },
                role: types_1.UserRole.STUDENT,
                isEmailVerified: decodedToken.email_verified || false,
            });
            await user.save();
            logger_1.logger.info(`New user created from Google: ${decodedToken.email}`);
        }
        else {
            // Update Firebase UID if not set
            if (!user.firebaseUid) {
                user.firebaseUid = decodedToken.uid;
                await user.save();
            }
        }
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ userId: user._id, email: user.email, role: user.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
        res.json({
            success: true,
            data: {
                user,
                token,
            },
            message: "Google login successful",
        });
    }
    catch (error) {
        logger_1.logger.error("Google login error:", error);
        res.status(500).json({
            success: false,
            message: "Google login failed",
        });
    }
};
exports.googleLogin = googleLogin;
const logout = async (req, res) => {
    try {
        // In a production app, you'd want to blacklist the token
        // For now, we'll just send a success response
        res.json({
            success: true,
            message: "Logout successful",
        });
    }
    catch (error) {
        logger_1.logger.error("Logout error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.logout = logout;
const refreshToken = async (req, res) => {
    try {
        // Implementation for token refresh
        res.json({
            success: true,
            message: "Token refresh not implemented yet",
        });
    }
    catch (error) {
        logger_1.logger.error("Refresh token error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.refreshToken = refreshToken;
const verifyEmail = async (req, res) => {
    try {
        // Implementation for email verification
        res.json({
            success: true,
            message: "Email verification not implemented yet",
        });
    }
    catch (error) {
        logger_1.logger.error("Email verification error:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
exports.verifyEmail = verifyEmail;
//# sourceMappingURL=authController.js.map