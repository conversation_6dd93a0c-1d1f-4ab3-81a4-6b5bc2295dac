name: Deploy Time Course Platform

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        
    - name: Install dependencies for all services
      run: |
        cd services/auth-service && npm ci
        cd ../course-service && npm ci
        cd ../payment-service && npm ci
        cd ../analytics-service && npm ci
        
    - name: Run tests for all services
      run: |
        cd services/auth-service && npm test
        cd ../course-service && npm test
        cd ../payment-service && npm test
        cd ../analytics-service && npm test

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Create environment file
      run: |
        echo "NODE_ENV=${{ github.ref == 'refs/heads/main' && 'production' || 'development' }}" > .env
        echo "JWT_SECRET=${{ secrets.JWT_SECRET }}" >> .env
        echo "FIREBASE_PROJECT_ID=${{ secrets.FIREBASE_PROJECT_ID }}" >> .env
        echo "FIREBASE_PRIVATE_KEY=${{ secrets.FIREBASE_PRIVATE_KEY }}" >> .env
        echo "FIREBASE_CLIENT_EMAIL=${{ secrets.FIREBASE_CLIENT_EMAIL }}" >> .env
        echo "BUNNY_API_KEY=${{ secrets.BUNNY_API_KEY }}" >> .env
        echo "BUNNY_STORAGE_ZONE=${{ secrets.BUNNY_STORAGE_ZONE }}" >> .env
        echo "XENDIT_SECRET_KEY=${{ secrets.XENDIT_SECRET_KEY }}" >> .env
        echo "XENDIT_WEBHOOK_TOKEN=${{ secrets.XENDIT_WEBHOOK_TOKEN }}" >> .env
    
    - name: Build and start services
      run: |
        docker-compose build
        docker-compose up -d
        
    - name: Wait for services to be ready
      run: |
        sleep 30
        docker-compose ps
        
    - name: Health check
      run: |
        curl -f http://localhost/api/auth/health || exit 1
        curl -f http://localhost/api/courses/health || exit 1
        curl -f http://localhost/api/payments/health || exit 1
        curl -f http://localhost/api/analytics/health || exit 1

  deploy-production:
    if: github.ref == 'refs/heads/main'
    needs: build-and-deploy
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production server..."
        # Add your production deployment commands here