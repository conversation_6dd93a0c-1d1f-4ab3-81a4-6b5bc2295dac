"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Assignment = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const types_1 = require("../shared/types");
const questionSchema = new mongoose_1.Schema({
    text: { type: String, required: true },
    type: {
        type: String,
        enum: Object.values(types_1.QuestionType),
        required: true,
    },
    options: [{ type: String }], // for multiple choice
    correctAnswer: { type: String, required: true },
    points: { type: Number, required: true, min: 1 },
    order: { type: Number, required: true },
});
const assignmentSchema = new mongoose_1.Schema({
    lessonId: { type: String, required: true, index: true },
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    questions: [questionSchema],
    triggerTime: { type: Number, required: true }, // timestamp in video (seconds)
    timeLimit: { type: Number, required: true }, // in minutes
    passingScore: { type: Number, required: true, min: 0, max: 100 }, // percentage
    maxAttempts: { type: Number, default: 1 },
    isActive: { type: Boolean, default: true },
}, {
    timestamps: true,
});
// Index for lesson and trigger time
assignmentSchema.index({ lessonId: 1, triggerTime: 1 });
exports.Assignment = mongoose_1.default.model("Assignment", assignmentSchema);
//# sourceMappingURL=Assignment.js.map