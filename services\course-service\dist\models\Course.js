"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Course = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const types_1 = require("../shared/types");
const courseSchema = new mongoose_1.Schema({
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    category: {
        type: String,
        enum: Object.values(types_1.CourseCategory),
        required: true,
    },
    tutorId: { type: String, required: true, index: true },
    duration: { type: Number, required: true }, // in days
    totalModules: { type: Number, required: true },
    price: { type: Number, required: true, min: 0 },
    currency: { type: String, default: "USD" },
    status: {
        type: String,
        enum: Object.values(types_1.CourseStatus),
        default: types_1.CourseStatus.DRAFT,
    },
    thumbnail: { type: String },
    tags: [{ type: String }],
    requirements: [{ type: String }],
    learningOutcomes: [{ type: String }],
}, {
    timestamps: true,
});
// Indexes for better performance
courseSchema.index({ category: 1, status: 1 });
courseSchema.index({ tutorId: 1 });
courseSchema.index({ createdAt: -1 });
courseSchema.index({ title: "text", description: "text" });
exports.Course = mongoose_1.default.model("Course", courseSchema);
//# sourceMappingURL=Course.js.map