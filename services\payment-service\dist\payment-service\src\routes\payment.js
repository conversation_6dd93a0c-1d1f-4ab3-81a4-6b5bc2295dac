"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const paymentController_1 = require("../controllers/paymentController");
const router = express_1.default.Router();
// Create payment (student only)
router.post('/', [
    auth_1.auth,
    (0, auth_1.authorize)(types_1.UserRole.STUDENT),
    (0, express_validator_1.body)('courseId').notEmpty().withMessage('Course ID is required'),
    (0, express_validator_1.body)('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
    (0, express_validator_1.body)('courseDuration').isInt({ min: 1 }).withMessage('Course duration must be a positive integer'),
    validation_1.validate
], paymentController_1.createPayment);
// Get payment status
router.get('/:id', auth_1.auth, paymentController_1.getPaymentStatus);
// Get my payments (student only)
router.get('/my/payments', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.STUDENT), paymentController_1.getMyPayments);
exports.default = router;
//# sourceMappingURL=payment.js.map