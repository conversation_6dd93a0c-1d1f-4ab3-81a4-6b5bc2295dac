interface XenditInvoiceResponse {
    id: string;
    external_id: string;
    user_id: string;
    status: string;
    merchant_name: string;
    amount: number;
    description: string;
    invoice_url: string;
    expiry_date: string;
    created: string;
    updated: string;
}
export declare class XenditService {
    private apiKey;
    private webhookToken;
    private baseUrl;
    constructor();
    /**
     * Create payment invoice
     */
    createInvoice(externalId: string, amount: number, description: string, customerName: string, customerEmail: string, duration?: number): Promise<XenditInvoiceResponse>;
    /**
     * Get invoice by ID
     */
    getInvoice(invoiceId: string): Promise<XenditInvoiceResponse>;
    /**
     * Verify webhook signature
     */
    verifyWebhookSignature(rawBody: string, signature: string): boolean;
    /**
     * Process webhook payload
     */
    processWebhook(payload: any): {
        event: string;
        invoiceId: string;
        externalId: string;
        status: string;
        amount: number;
        paidAt?: Date;
    };
}
export declare const xenditService: XenditService;
export {};
//# sourceMappingURL=xenditService.d.ts.map