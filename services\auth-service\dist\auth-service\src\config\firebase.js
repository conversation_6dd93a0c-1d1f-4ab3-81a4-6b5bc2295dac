"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyFirebaseToken = exports.initializeFirebase = void 0;
const firebase_admin_1 = __importDefault(require("firebase-admin"));
const logger_1 = require("../utils/logger");
const initializeFirebase = async () => {
    try {
        if (!process.env.FIREBASE_PROJECT_ID || !process.env.FIREBASE_PRIVATE_KEY || !process.env.FIREBASE_CLIENT_EMAIL) {
            throw new Error('Firebase configuration environment variables are missing');
        }
        const serviceAccount = {
            projectId: process.env.FIREBASE_PROJECT_ID,
            privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        };
        firebase_admin_1.default.initializeApp({
            credential: firebase_admin_1.default.credential.cert(serviceAccount),
            projectId: process.env.FIREBASE_PROJECT_ID,
        });
        logger_1.logger.info('Firebase Admin initialized successfully');
    }
    catch (error) {
        logger_1.logger.error('Error initializing Firebase:', error);
        throw error;
    }
};
exports.initializeFirebase = initializeFirebase;
const verifyFirebaseToken = async (token) => {
    try {
        return await firebase_admin_1.default.auth().verifyIdToken(token);
    }
    catch (error) {
        logger_1.logger.error('Error verifying Firebase token:', error);
        throw error;
    }
};
exports.verifyFirebaseToken = verifyFirebaseToken;
//# sourceMappingURL=firebase.js.map