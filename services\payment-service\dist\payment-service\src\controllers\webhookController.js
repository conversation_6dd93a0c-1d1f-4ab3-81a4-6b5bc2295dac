"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleXenditWebhook = void 0;
const Payment_1 = require("../models/Payment");
const types_1 = require("../shared/types");
const xenditService_1 = require("../services/xenditService");
const subscriptionService_1 = require("../services/subscriptionService");
const logger_1 = require("../utils/logger");
const handleXenditWebhook = async (req, res) => {
    try {
        const signature = req.headers['x-callback-token'];
        const rawBody = JSON.stringify(req.body);
        // Verify webhook signature
        if (!xenditService_1.xenditService.verifyWebhookSignature(rawBody, signature)) {
            logger_1.logger.warn('Invalid webhook signature');
            return res.status(400).json({
                success: false,
                message: 'Invalid signature'
            });
        }
        const webhookData = xenditService_1.xenditService.processWebhook(req.body);
        logger_1.logger.info(`Webhook received: ${webhookData.event} for ${webhookData.externalId}`);
        // Find payment by external ID
        const payment = await Payment_1.Payment.findOne({ externalId: webhookData.externalId });
        if (!payment) {
            logger_1.logger.warn(`Payment not found for external ID: ${webhookData.externalId}`);
            return res.status(404).json({
                success: false,
                message: 'Payment not found'
            });
        }
        // Update payment based on webhook event
        switch (webhookData.status) {
            case 'PAID':
                if (payment.status !== types_1.PaymentStatus.COMPLETED) {
                    payment.status = types_1.PaymentStatus.COMPLETED;
                    payment.transactionId = webhookData.invoiceId;
                    payment.paidAt = webhookData.paidAt || new Date();
                    payment.webhookData = req.body;
                    await payment.save();
                    // Create subscription
                    try {
                        await subscriptionService_1.subscriptionService.createSubscription(payment.studentId, payment.courseId, payment.id, 90, // Default 90 days - should come from course data
                        payment.amount, payment.currency);
                        logger_1.logger.info(`Subscription created for payment: ${payment.id}`);
                    }
                    catch (error) {
                        logger_1.logger.error('Error creating subscription from webhook:', error);
                    }
                }
                break;
            case 'EXPIRED':
                payment.status = types_1.PaymentStatus.FAILED;
                payment.failureReason = 'Payment expired';
                payment.webhookData = req.body;
                await payment.save();
                break;
            case 'FAILED':
                payment.status = types_1.PaymentStatus.FAILED;
                payment.failureReason = 'Payment failed';
                payment.webhookData = req.body;
                await payment.save();
                break;
            default:
                logger_1.logger.info(`Unhandled webhook status: ${webhookData.status}`);
        }
        res.json({
            success: true,
            message: 'Webhook processed successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing webhook:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.handleXenditWebhook = handleXenditWebhook;
//# sourceMappingURL=webhookController.js.map