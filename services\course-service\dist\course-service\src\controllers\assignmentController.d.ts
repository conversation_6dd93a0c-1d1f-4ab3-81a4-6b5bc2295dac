import { Request, Response } from 'express';
interface AuthRequest extends Request {
    user?: any;
}
export declare const createAssignment: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAssignmentsByLesson: (req: Request, res: Response) => Promise<void>;
export declare const getAssignmentById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const submitAssignment: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAssignmentSubmissions: (req: AuthRequest, res: Response) => Promise<void>;
export declare const updateAssignment: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteAssignment: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export {};
//# sourceMappingURL=assignmentController.d.ts.map