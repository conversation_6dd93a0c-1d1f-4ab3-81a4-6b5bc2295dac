import mongoose, { Document } from 'mongoose';
import { AssignmentSubmission as IAssignmentSubmission } from '../shared/types';
export interface AssignmentSubmissionDocument extends IAssignmentSubmission, Document {
}
export declare const AssignmentSubmission: mongoose.Model<AssignmentSubmissionDocument, {}, {}, {}, mongoose.Document<unknown, {}, AssignmentSubmissionDocument, {}, {}> & AssignmentSubmissionDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=AssignmentSubmission.d.ts.map