"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserAnalytics = exports.CourseAnalytics = exports.PlatformAnalytics = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const platformAnalyticsSchema = new mongoose_1.Schema({
    date: { type: Date, required: true, unique: true },
    totalUsers: { type: Number, default: 0 },
    activeSubscriptions: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    newRegistrations: { type: Number, default: 0 },
    courseCompletions: { type: Number, default: 0 },
    assignmentFailures: { type: Number, default: 0 },
    terminatedSubscriptions: { type: Number, default: 0 },
    coachingSessionsCreated: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});
const courseAnalyticsSchema = new mongoose_1.Schema({
    courseId: { type: String, required: true, unique: true },
    tutorId: { type: String, required: true },
    title: { type: String, required: true },
    category: { type: String, required: true },
    totalEnrollments: { type: Number, default: 0 },
    activeEnrollments: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0 },
    averageProgress: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    assignmentFailureRate: { type: Number, default: 0 },
    lastUpdated: { type: Date, default: Date.now }
});
const userAnalyticsSchema = new mongoose_1.Schema({
    userId: { type: String, required: true, unique: true },
    role: { type: String, required: true },
    totalCourses: { type: Number, default: 0 },
    completedCourses: { type: Number, default: 0 },
    activeSubscriptions: { type: Number, default: 0 },
    totalSpent: { type: Number, default: 0 },
    averageProgress: { type: Number, default: 0 },
    assignmentFailures: { type: Number, default: 0 },
    lastActivity: { type: Date, default: Date.now },
    joinedDate: { type: Date, default: Date.now }
});
// Indexes for better performance
platformAnalyticsSchema.index({ date: -1 });
courseAnalyticsSchema.index({ tutorId: 1 });
courseAnalyticsSchema.index({ category: 1 });
userAnalyticsSchema.index({ role: 1 });
userAnalyticsSchema.index({ lastActivity: -1 });
exports.PlatformAnalytics = mongoose_1.default.model('PlatformAnalytics', platformAnalyticsSchema);
exports.CourseAnalytics = mongoose_1.default.model('CourseAnalytics', courseAnalyticsSchema);
exports.UserAnalytics = mongoose_1.default.model('UserAnalytics', userAnalyticsSchema);
//# sourceMappingURL=Analytics.js.map