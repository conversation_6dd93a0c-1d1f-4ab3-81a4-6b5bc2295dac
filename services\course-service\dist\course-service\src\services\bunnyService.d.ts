interface BunnyStreamingUrls {
    hlsUrl: string;
    dashUrl: string;
    mp4Url: string;
    thumbnailUrl: string;
}
export declare class BunnyService {
    private apiKey;
    private storageZone;
    private videoLibraryId;
    private baseUrl;
    constructor();
    /**
     * Upload video to Bunny.net
     */
    uploadVideo(videoBuffer: Buffer, title: string): Promise<string>;
    /**
     * Get streaming URLs for a video
     */
    getStreamingUrls(videoId: string): Promise<BunnyStreamingUrls>;
    /**
     * Generate secure video token for authenticated access
     */
    generateSecureToken(videoId: string, userId: string, expirationTime?: number): string;
    /**
     * Delete video from Bunny.net
     */
    deleteVideo(videoId: string): Promise<boolean>;
    /**
     * Get video analytics
     */
    getVideoAnalytics(videoId: string): Promise<any>;
}
export declare const bunnyService: BunnyService;
export {};
//# sourceMappingURL=bunnyService.d.ts.map