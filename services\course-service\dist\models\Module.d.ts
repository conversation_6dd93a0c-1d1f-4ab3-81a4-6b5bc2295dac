import mongoose, { Document } from "mongoose";
import { Module as IModule } from "../shared/types";
export interface ModuleDocument extends IModule, Document {
}
export declare const Module: mongoose.Model<ModuleDocument, {}, {}, {}, mongoose.Document<unknown, {}, ModuleDocument, {}, {}> & ModuleDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Module.d.ts.map