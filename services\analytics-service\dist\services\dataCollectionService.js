"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataCollectionService = exports.DataCollectionService = void 0;
exports.startDataCollection = startDataCollection;
const axios_1 = __importDefault(require("axios"));
const node_cron_1 = __importDefault(require("node-cron"));
const Analytics_1 = require("../models/Analytics");
const logger_1 = require("../utils/logger");
const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
const COURSE_SERVICE_URL = process.env.COURSE_SERVICE_URL || 'http://localhost:3002';
const PAYMENT_SERVICE_URL = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3003';
class DataCollectionService {
    /**
     * Collect data from all services and update analytics
     */
    async collectAllData() {
        try {
            logger_1.logger.info('Starting data collection...');
            await Promise.all([
                this.collectPlatformAnalytics(),
                this.collectCourseAnalytics(),
                this.collectUserAnalytics()
            ]);
            logger_1.logger.info('Data collection completed successfully');
        }
        catch (error) {
            logger_1.logger.error('Error during data collection:', error);
        }
    }
    /**
     * Collect platform-wide analytics
     */
    async collectPlatformAnalytics() {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            // Get data from all services
            const [usersData, subscriptionsData, paymentsData] = await Promise.all([
                this.fetchFromService(`${AUTH_SERVICE_URL}/api/users?limit=1000`),
                this.fetchFromService(`${PAYMENT_SERVICE_URL}/api/subscriptions?limit=1000`),
                this.fetchFromService(`${PAYMENT_SERVICE_URL}/api/payments/my/payments?limit=1000`)
            ]);
            const totalUsers = usersData?.data?.total || 0;
            const activeSubscriptions = subscriptionsData?.data?.subscriptions?.filter((s) => s.status === 'active').length || 0;
            const totalRevenue = paymentsData?.data?.payments?.reduce((sum, p) => p.status === 'completed' ? sum + p.amount : sum, 0) || 0;
            // Update or create platform analytics
            await Analytics_1.PlatformAnalytics.findOneAndUpdate({ date: today }, {
                totalUsers,
                activeSubscriptions,
                totalRevenue,
                lastUpdated: new Date()
            }, { upsert: true, new: true });
            logger_1.logger.info(`Platform analytics updated: ${totalUsers} users, ${activeSubscriptions} active subscriptions`);
        }
        catch (error) {
            logger_1.logger.error('Error collecting platform analytics:', error);
        }
    }
    /**
     * Collect course-specific analytics
     */
    async collectCourseAnalytics() {
        try {
            const coursesData = await this.fetchFromService(`${COURSE_SERVICE_URL}/api/courses?limit=1000`);
            const courses = coursesData?.data?.courses || [];
            for (const course of courses) {
                // Get subscriptions for this course
                const subscriptionsData = await this.fetchFromService(`${PAYMENT_SERVICE_URL}/api/subscriptions?courseId=${course.id}`);
                const subscriptions = subscriptionsData?.data?.subscriptions || [];
                const totalEnrollments = subscriptions.length;
                const activeEnrollments = subscriptions.filter((s) => s.status === 'active').length;
                const completionRate = totalEnrollments > 0 ?
                    (subscriptions.filter((s) => s.progress?.completionPercentage === 100).length / totalEnrollments) * 100 : 0;
                await Analytics_1.CourseAnalytics.findOneAndUpdate({ courseId: course.id }, {
                    tutorId: course.tutorId,
                    title: course.title,
                    category: course.category,
                    totalEnrollments,
                    activeEnrollments,
                    completionRate,
                    lastUpdated: new Date()
                }, { upsert: true, new: true });
            }
            logger_1.logger.info(`Course analytics updated for ${courses.length} courses`);
        }
        catch (error) {
            logger_1.logger.error('Error collecting course analytics:', error);
        }
    }
    /**
     * Collect user-specific analytics
     */
    async collectUserAnalytics() {
        try {
            const usersData = await this.fetchFromService(`${AUTH_SERVICE_URL}/api/users?limit=1000`);
            const users = usersData?.data?.users || [];
            for (const user of users) {
                if (user.role === 'student') {
                    // Get user's subscriptions
                    const subscriptionsData = await this.fetchFromService(`${PAYMENT_SERVICE_URL}/api/subscriptions?studentId=${user.id}`);
                    const subscriptions = subscriptionsData?.data?.subscriptions || [];
                    const totalCourses = subscriptions.length;
                    const activeSubscriptions = subscriptions.filter((s) => s.status === 'active').length;
                    const completedCourses = subscriptions.filter((s) => s.progress?.completionPercentage === 100).length;
                    await Analytics_1.UserAnalytics.findOneAndUpdate({ userId: user.id }, {
                        role: user.role,
                        totalCourses,
                        completedCourses,
                        activeSubscriptions,
                        lastActivity: user.lastLogin || user.updatedAt,
                        joinedDate: user.createdAt
                    }, { upsert: true, new: true });
                }
            }
            logger_1.logger.info(`User analytics updated for ${users.length} users`);
        }
        catch (error) {
            logger_1.logger.error('Error collecting user analytics:', error);
        }
    }
    /**
     * Fetch data from a service with error handling
     */
    async fetchFromService(url) {
        try {
            const response = await axios_1.default.get(url, {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to fetch from ${url}:`, error.message);
            return null;
        }
    }
    /**
     * Get platform dashboard data
     */
    async getPlatformDashboard() {
        try {
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            const [latestAnalytics, monthlyTrend, topCourses] = await Promise.all([
                Analytics_1.PlatformAnalytics.findOne().sort({ date: -1 }),
                Analytics_1.PlatformAnalytics.find({
                    date: { $gte: thirtyDaysAgo }
                }).sort({ date: 1 }),
                Analytics_1.CourseAnalytics.find().sort({ totalEnrollments: -1 }).limit(10)
            ]);
            return {
                overview: latestAnalytics || {},
                monthlyTrend: monthlyTrend || [],
                topCourses: topCourses || []
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting platform dashboard:', error);
            throw error;
        }
    }
    /**
     * Get tutor dashboard data
     */
    async getTutorDashboard(tutorId) {
        try {
            const [tutorCourses, totalStudents] = await Promise.all([
                Analytics_1.CourseAnalytics.find({ tutorId }).sort({ totalEnrollments: -1 }),
                Analytics_1.CourseAnalytics.aggregate([
                    { $match: { tutorId } },
                    { $group: { _id: null, total: { $sum: '$totalEnrollments' } } }
                ])
            ]);
            return {
                courses: tutorCourses || [],
                totalStudents: totalStudents[0]?.total || 0,
                totalCourses: tutorCourses.length
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting tutor dashboard:', error);
            throw error;
        }
    }
}
exports.DataCollectionService = DataCollectionService;
exports.dataCollectionService = new DataCollectionService();
/**
 * Start automated data collection
 */
function startDataCollection() {
    // Run data collection every hour
    node_cron_1.default.schedule('0 * * * *', async () => {
        await exports.dataCollectionService.collectAllData();
    });
    // Run initial collection after 30 seconds
    setTimeout(async () => {
        await exports.dataCollectionService.collectAllData();
    }, 30000);
    logger_1.logger.info('Data collection service started');
}
//# sourceMappingURL=dataCollectionService.js.map