"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const Analytics_1 = require("../models/Analytics");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
// Generate platform report (Admin only)
router.get('/platform', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        const filter = {};
        if (startDate && endDate) {
            filter.date = {
                $gte: new Date(startDate),
                $lte: new Date(endDate)
            };
        }
        const analytics = await Analytics_1.PlatformAnalytics.find(filter).sort({ date: -1 });
        res.json({
            success: true,
            data: analytics
        });
    }
    catch (error) {
        logger_1.logger.error('Error generating platform report:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Generate course performance report
router.get('/courses', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), async (req, res) => {
    try {
        const { tutorId } = req.query;
        const filter = {};
        if (req.user.role === types_1.UserRole.TUTOR) {
            filter.tutorId = req.user.id;
        }
        else if (tutorId) {
            filter.tutorId = tutorId;
        }
        const courses = await Analytics_1.CourseAnalytics.find(filter).sort({ totalEnrollments: -1 });
        res.json({
            success: true,
            data: courses
        });
    }
    catch (error) {
        logger_1.logger.error('Error generating course report:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Generate user activity report (Admin only)
router.get('/users', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), async (req, res) => {
    try {
        const { role } = req.query;
        const filter = {};
        if (role) {
            filter.role = role;
        }
        const users = await Analytics_1.UserAnalytics.find(filter).sort({ lastActivity: -1 });
        res.json({
            success: true,
            data: users
        });
    }
    catch (error) {
        logger_1.logger.error('Error generating user report:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=reports.js.map