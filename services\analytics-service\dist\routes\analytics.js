"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const dataCollectionService_1 = require("../services/dataCollectionService");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
// Trigger manual data collection (Admin only)
router.post('/collect', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), async (req, res) => {
    try {
        await dataCollectionService_1.dataCollectionService.collectAllData();
        res.json({
            success: true,
            message: 'Data collection completed successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Error in manual data collection:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get platform analytics (Admin only)
router.get('/platform', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), async (req, res) => {
    try {
        const dashboard = await dataCollectionService_1.dataCollectionService.getPlatformDashboard();
        res.json({
            success: true,
            data: dashboard
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting platform analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
// Get tutor analytics
router.get('/tutor/:tutorId', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), async (req, res) => {
    try {
        const { tutorId } = req.params;
        // Tutors can only view their own analytics unless they're admin
        if (req.user.role !== types_1.UserRole.ADMIN && req.user.id !== tutorId) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }
        const dashboard = await dataCollectionService_1.dataCollectionService.getTutorDashboard(tutorId);
        res.json({
            success: true,
            data: dashboard
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting tutor analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=analytics.js.map