version: '3.8'

services:
  # Auth & User Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongo-auth:27017/time_course_auth
      - JWT_SECRET=your_jwt_secret_key_here
      - FIREBASE_PROJECT_ID=your_firebase_project_id
      - FIREBASE_PRIVATE_KEY=your_firebase_private_key
      - FIREBASE_CLIENT_EMAIL=your_firebase_client_email
    depends_on:
      - mongo-auth
    networks:
      - time-course-network

  # Course & Learning Service
  course-service:
    build:
      context: ./services/course-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb://mongo-course:27017/time_course_courses
      - BUNNY_API_KEY=your_bunny_api_key
      - BUNNY_STORAGE_ZONE=your_bunny_storage_zone
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - mongo-course
    networks:
      - time-course-network

  # Payment & Subscription Service
  payment-service:
    build:
      context: ./services/payment-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - MONGODB_URI=mongodb://mongo-payment:27017/time_course_payments
      - XENDIT_SECRET_KEY=your_xendit_secret_key
      - XENDIT_WEBHOOK_TOKEN=your_xendit_webhook_token
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - mongo-payment
    networks:
      - time-course-network

  # Reporting & Analytics Service
  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - MONGODB_URI=mongodb://mongo-analytics:27017/time_course_analytics
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COURSE_SERVICE_URL=http://course-service:3002
      - PAYMENT_SERVICE_URL=http://payment-service:3003
    depends_on:
      - mongo-analytics
    networks:
      - time-course-network

  # MongoDB instances for each service
  mongo-auth:
    image: mongo:7.0
    ports:
      - "27017:27017"
    volumes:
      - mongo-auth-data:/data/db
    networks:
      - time-course-network

  mongo-course:
    image: mongo:7.0
    ports:
      - "27018:27017"
    volumes:
      - mongo-course-data:/data/db
    networks:
      - time-course-network

  mongo-payment:
    image: mongo:7.0
    ports:
      - "27019:27017"
    volumes:
      - mongo-payment-data:/data/db
    networks:
      - time-course-network

  mongo-analytics:
    image: mongo:7.0
    ports:
      - "27020:27017"
    volumes:
      - mongo-analytics-data:/data/db
    networks:
      - time-course-network

  # API Gateway (Optional - can use nginx)
  api-gateway:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - auth-service
      - course-service
      - payment-service
      - analytics-service
    networks:
      - time-course-network

volumes:
  mongo-auth-data:
  mongo-course-data:
  mongo-payment-data:
  mongo-analytics-data:

networks:
  time-course-network:
    driver: bridge