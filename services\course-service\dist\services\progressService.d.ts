export interface PacingStatus {
    isOnTrack: boolean;
    expectedProgress: number;
    actualProgress: number;
    daysElapsed: number;
    totalDays: number;
    warningMessage?: string;
}
export declare class ProgressService {
    /**
     * Initialize progress tracking for a new subscription
     */
    initializeProgress(studentId: string, courseId: string, subscriptionId: string): Promise<void>;
    /**
     * Update progress when a lesson is completed
     */
    completeLesson(studentId: string, courseId: string, lessonId: string): Promise<void>;
    /**
     * Update progress when an assignment is completed
     */
    completeAssignment(studentId: string, courseId: string, assignmentId: string): Promise<void>;
    /**
     * Calculate pacing status for a student
     */
    calculatePacing(studentId: string, courseId: string, subscriptionStartDate: Date, courseDuration: number): Promise<PacingStatus>;
    /**
     * Get detailed progress information
     */
    getProgressDetails(studentId: string, courseId: string): Promise<any>;
    /**
     * Update completion percentage
     */
    private updateCompletionPercentage;
}
export declare const progressService: ProgressService;
//# sourceMappingURL=progressService.d.ts.map