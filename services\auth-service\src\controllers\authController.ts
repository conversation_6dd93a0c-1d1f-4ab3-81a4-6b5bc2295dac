import { Request, Response } from "express";
import jwt from "jsonwebtoken";
import { User } from "../models/User";
import { verifyFirebaseToken } from "../config/firebase";
import { UserRole } from "../shared/types";
import { logger } from "../utils/logger";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";

export const register = async (req: Request, res: Response) => {
  try {
    const { email, password, profile, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User already exists with this email",
      });
    }

    // Only admin can create tutors
    const userRole =
      role === UserRole.TUTOR ? UserRole.STUDENT : role || UserRole.STUDENT;

    // Create new user
    const user = new User({
      email,
      password,
      profile,
      role: userRole,
      isEmailVerified: true, // Auto-verify for now
    });

    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role },
      JWT_SECRET as string,
      { expiresIn: JWT_EXPIRES_IN }
    );

    logger.info(`User registered: ${email}`);

    res.status(201).json({
      success: true,
      data: {
        user,
        token,
      },
      message: "User registered successfully",
    });
  } catch (error) {
    logger.error("Registration error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role },
      JWT_SECRET as string,
      { expiresIn: JWT_EXPIRES_IN }
    );

    logger.info(`User logged in: ${email}`);

    res.json({
      success: true,
      data: {
        user,
        token,
      },
      message: "Login successful",
    });
  } catch (error) {
    logger.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const googleLogin = async (req: Request, res: Response) => {
  try {
    const { idToken } = req.body;

    // Verify Firebase token
    const decodedToken = await verifyFirebaseToken(idToken);

    let user = await User.findOne({
      $or: [{ firebaseUid: decodedToken.uid }, { email: decodedToken.email }],
    });

    if (!user) {
      // Create new user from Google data
      user = new User({
        email: decodedToken.email,
        firebaseUid: decodedToken.uid,
        profile: {
          firstName: decodedToken.name?.split(" ")[0] || "",
          lastName: decodedToken.name?.split(" ").slice(1).join(" ") || "",
          avatar: decodedToken.picture,
        },
        role: UserRole.STUDENT,
        isEmailVerified: decodedToken.email_verified || false,
      });

      await user.save();
      logger.info(`New user created from Google: ${decodedToken.email}`);
    } else {
      // Update Firebase UID if not set
      if (!user.firebaseUid) {
        user.firebaseUid = decodedToken.uid;
        await user.save();
      }
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role },
      JWT_SECRET as string,
      { expiresIn: JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      data: {
        user,
        token,
      },
      message: "Google login successful",
    });
  } catch (error) {
    logger.error("Google login error:", error);
    res.status(500).json({
      success: false,
      message: "Google login failed",
    });
  }
};

export const logout = async (req: Request, res: Response) => {
  try {
    // In a production app, you'd want to blacklist the token
    // For now, we'll just send a success response
    res.json({
      success: true,
      message: "Logout successful",
    });
  } catch (error) {
    logger.error("Logout error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const refreshToken = async (req: Request, res: Response) => {
  try {
    // Implementation for token refresh
    res.json({
      success: true,
      message: "Token refresh not implemented yet",
    });
  } catch (error) {
    logger.error("Refresh token error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

export const verifyEmail = async (req: Request, res: Response) => {
  try {
    // Implementation for email verification
    res.json({
      success: true,
      message: "Email verification not implemented yet",
    });
  } catch (error) {
    logger.error("Email verification error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};
