import mongoose, { Document } from "mongoose";
import { Course as ICourse } from "../shared/types";
export interface CourseDocument extends ICourse, Document {
}
export declare const Course: mongoose.Model<CourseDocument, {}, {}, {}, mongoose.Document<unknown, {}, CourseDocument, {}, {}> & CourseDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Course.d.ts.map