"use strict";
// Shared types across all microservices
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoachingStatus = exports.PaymentStatus = exports.SubscriptionStatus = exports.QuestionType = exports.MaterialType = exports.CourseStatus = exports.CourseCategory = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["STUDENT"] = "student";
    UserRole["TUTOR"] = "tutor";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
var CourseCategory;
(function (CourseCategory) {
    CourseCategory["IELTS"] = "ielts";
    CourseCategory["TOEFL"] = "toefl";
    CourseCategory["TOEIC"] = "toeic";
    CourseCategory["GENERAL_ENGLISH"] = "general_english";
})(CourseCategory || (exports.CourseCategory = CourseCategory = {}));
var CourseStatus;
(function (CourseStatus) {
    CourseStatus["DRAFT"] = "draft";
    CourseStatus["PUBLISHED"] = "published";
    CourseStatus["ARCHIVED"] = "archived";
})(CourseStatus || (exports.CourseStatus = CourseStatus = {}));
var MaterialType;
(function (MaterialType) {
    MaterialType["PDF"] = "pdf";
    MaterialType["PPT"] = "ppt";
    MaterialType["DOC"] = "doc";
})(MaterialType || (exports.MaterialType = MaterialType = {}));
var QuestionType;
(function (QuestionType) {
    QuestionType["SHORT_ANSWER"] = "short_answer";
    QuestionType["ESSAY"] = "essay";
    QuestionType["MULTIPLE_CHOICE"] = "multiple_choice";
})(QuestionType || (exports.QuestionType = QuestionType = {}));
var SubscriptionStatus;
(function (SubscriptionStatus) {
    SubscriptionStatus["ACTIVE"] = "active";
    SubscriptionStatus["EXPIRED"] = "expired";
    SubscriptionStatus["TERMINATED"] = "terminated";
    SubscriptionStatus["PAUSED"] = "paused";
})(SubscriptionStatus || (exports.SubscriptionStatus = SubscriptionStatus = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "pending";
    PaymentStatus["COMPLETED"] = "completed";
    PaymentStatus["FAILED"] = "failed";
    PaymentStatus["REFUNDED"] = "refunded";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
var CoachingStatus;
(function (CoachingStatus) {
    CoachingStatus["SCHEDULED"] = "scheduled";
    CoachingStatus["IN_PROGRESS"] = "in_progress";
    CoachingStatus["COMPLETED"] = "completed";
    CoachingStatus["CANCELLED"] = "cancelled";
})(CoachingStatus || (exports.CoachingStatus = CoachingStatus = {}));
//# sourceMappingURL=index.js.map