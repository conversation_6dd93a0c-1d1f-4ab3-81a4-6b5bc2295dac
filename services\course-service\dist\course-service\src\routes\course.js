"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const types_1 = require("../shared/types");
const courseController_1 = require("../controllers/courseController");
const router = express_1.default.Router();
// Get all courses (public)
router.get('/', courseController_1.getAllCourses);
// Get course by ID (public)
router.get('/:id', courseController_1.getCourseById);
// Get my courses (tutor/admin only)
router.get('/my/courses', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), courseController_1.getMyCourses);
// Create course (tutor/admin only)
router.post('/', [
    auth_1.auth,
    (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN),
    (0, express_validator_1.body)('title').notEmpty().withMessage('Course title is required'),
    (0, express_validator_1.body)('description').notEmpty().withMessage('Course description is required'),
    (0, express_validator_1.body)('category').notEmpty().withMessage('Course category is required'),
    (0, express_validator_1.body)('duration').isInt({ min: 1 }).withMessage('Duration must be a positive integer'),
    (0, express_validator_1.body)('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
    validation_1.validate
], courseController_1.createCourse);
// Update course (tutor/admin only)
router.put('/:id', [
    auth_1.auth,
    (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN),
    validation_1.validate
], courseController_1.updateCourse);
// Delete course (tutor/admin only)
router.delete('/:id', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), courseController_1.deleteCourse);
// Publish course (tutor/admin only)
router.patch('/:id/publish', auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.TUTOR, types_1.UserRole.ADMIN), courseController_1.publishCourse);
exports.default = router;
//# sourceMappingURL=course.js.map