export interface User {
    _id?: string;
    email: string;
    role: UserRole;
    profile: UserProfile;
    isActive: boolean;
    isEmailVerified: boolean;
    lastLogin?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum UserRole {
    STUDENT = "student",
    TUTOR = "tutor",
    ADMIN = "admin"
}
export interface UserProfile {
    firstName: string;
    lastName: string;
    avatar?: string;
    phone?: string;
    country?: string;
    language?: string;
}
export interface Course {
    title: string;
    description: string;
    category: CourseCategory;
    tutorId: string;
    duration: number;
    totalModules: number;
    price: number;
    currency: string;
    thumbnail?: string;
    status: CourseStatus;
    tags: string[];
    requirements: string[];
    learningOutcomes: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare enum CourseCategory {
    IELTS = "ielts",
    TOEFL = "toefl",
    TOEIC = "toeic",
    GENERAL_ENGLISH = "general_english"
}
export declare enum CourseStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived"
}
export interface Module {
    courseId: string;
    title: string;
    description: string;
    order: number;
    isPublished: boolean;
    estimatedDuration: number;
    lessons: Lesson[];
    createdAt: Date;
    updatedAt: Date;
}
export interface Lesson {
    moduleId: string;
    title: string;
    description: string;
    videoUrl: string;
    videoId?: string;
    duration: number;
    materials: Material[];
    assignments: Assignment[];
    order: number;
    isPublished: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface Material {
    id: string;
    lessonId: string;
    type: MaterialType;
    title: string;
    url: string;
    order: number;
}
export declare enum MaterialType {
    PDF = "pdf",
    PPT = "ppt",
    DOC = "doc"
}
export interface Assignment {
    lessonId: string;
    title: string;
    description: string;
    questions: Question[];
    triggerTime: number;
    timeLimit: number;
    maxAttempts: number;
    passingScore: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface Question {
    id: string;
    assignmentId: string;
    text: string;
    type: QuestionType;
    options?: string[];
    correctAnswer: string;
    points: number;
    order: number;
}
export declare enum QuestionType {
    SHORT_ANSWER = "short_answer",
    ESSAY = "essay",
    MULTIPLE_CHOICE = "multiple_choice"
}
export interface Subscription {
    studentId: string;
    courseId: string;
    paymentId?: string;
    status: SubscriptionStatus;
    startDate: Date;
    endDate: Date;
    failureCount: number;
    maxFailures: number;
    isEligibleForCoaching: boolean;
    terminatedAt?: Date;
    terminationReason?: string;
    progress: Progress;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum SubscriptionStatus {
    ACTIVE = "active",
    EXPIRED = "expired",
    TERMINATED = "terminated",
    PAUSED = "paused"
}
export interface Progress {
    studentId: string;
    courseId: string;
    subscriptionId: string;
    completedLessons: string[];
    completedAssignments: string[];
    totalLessons: number;
    totalAssignments: number;
    completionPercentage: number;
    currentModule: number;
    lastAccessedAt: Date;
    startedAt: Date;
    completedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface Payment {
    studentId: string;
    courseId: string;
    amount: number;
    currency: string;
    status: PaymentStatus;
    paymentMethod: string;
    transactionId?: string;
    invoiceId?: string;
    externalId: string;
    paymentUrl?: string;
    expiryDate?: Date;
    paidAt?: Date;
    failureReason?: string;
    webhookData?: any;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum PaymentStatus {
    PENDING = "pending",
    COMPLETED = "completed",
    FAILED = "failed",
    REFUNDED = "refunded"
}
export interface CoachingSession {
    id: string;
    tutorId: string;
    courseId: string;
    title: string;
    description: string;
    scheduledDate: Date;
    duration: number;
    meetingLink: string;
    maxParticipants: number;
    enrolledStudents: string[];
    status: CoachingStatus;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum CoachingStatus {
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export interface Certificate {
    id: string;
    studentId: string;
    courseId: string;
    verificationCode: string;
    completionDate: Date;
    grade: number;
    certificateUrl: string;
    createdAt: Date;
}
export interface AssignmentSubmission {
    studentId: string;
    assignmentId: string;
    subscriptionId: string;
    answers: Answer[];
    score: number;
    isPassed: boolean;
    attemptNumber: number;
    timeSpent?: number;
    submittedAt: Date;
    gradedAt?: Date;
    feedback?: string;
}
export interface Answer {
    questionId: string;
    answer: string;
    isCorrect: boolean;
    points: number;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface JwtPayload {
    userId: string;
    email: string;
    role: UserRole;
    iat: number;
    exp: number;
}
export interface ServiceHealthCheck {
    service: string;
    status: "healthy" | "unhealthy";
    timestamp: Date;
    uptime: number;
}
//# sourceMappingURL=index.d.ts.map