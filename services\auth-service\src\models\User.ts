import mongoose, { Document, Schema } from "mongoose";
import bcrypt from "bcryptjs";
import { User as IUser, UserRole, UserProfile } from "../shared/types";

export interface UserDocument extends IUser, Document {
  password?: string;
  firebaseUid?: string;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const userProfileSchema = new Schema<UserProfile>({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  avatar: { type: String },
  phone: { type: String },
  country: { type: String },
  language: { type: String, default: "en" },
});

const userSchema = new Schema<UserDocument>({
  email: { type: String, required: true, unique: true, lowercase: true },
  password: { type: String }, // Optional for social login
  firebaseUid: { type: String }, // For Firebase authentication
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.STUDENT,
  },
  profile: { type: userProfileSchema, required: true },
  isActive: { type: <PERSON>olean, default: true },
  isEmailVerified: { type: <PERSON>olean, default: false },
  lastLogin: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Index for better performance
userSchema.index({ email: 1 });
userSchema.index({ firebaseUid: 1 });
userSchema.index({ role: 1 });

// Hash password before saving
userSchema.pre("save", async function (next) {
  if (!this.isModified("password") || !this.password) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Update updatedAt field
userSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Compare password method
userSchema.methods.comparePassword = async function (
  candidatePassword: string
): Promise<boolean> {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

// Remove password from JSON output
userSchema.methods.toJSON = function () {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.firebaseUid;
  return userObject;
};

export const User = mongoose.model<UserDocument>("User", userSchema);
