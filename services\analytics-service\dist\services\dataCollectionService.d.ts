export declare class DataCollectionService {
    /**
     * Collect data from all services and update analytics
     */
    collectAllData(): Promise<void>;
    /**
     * Collect platform-wide analytics
     */
    private collectPlatformAnalytics;
    /**
     * Collect course-specific analytics
     */
    private collectCourseAnalytics;
    /**
     * Collect user-specific analytics
     */
    private collectUserAnalytics;
    /**
     * Fetch data from a service with error handling
     */
    private fetchFromService;
    /**
     * Get platform dashboard data
     */
    getPlatformDashboard(): Promise<any>;
    /**
     * Get tutor dashboard data
     */
    getTutorDashboard(tutorId: string): Promise<any>;
}
export declare const dataCollectionService: DataCollectionService;
/**
 * Start automated data collection
 */
export declare function startDataCollection(): void;
//# sourceMappingURL=dataCollectionService.d.ts.map