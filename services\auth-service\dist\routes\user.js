"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const types_1 = require("../shared/types");
const userController_1 = require("../controllers/userController");
const router = express_1.default.Router();
// Get all users (Admin only)
router.get("/", auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), userController_1.getAllUsers);
// Get user by ID
router.get("/:id", auth_1.auth, userController_1.getUserById);
// Update user
router.put("/:id", [
    auth_1.auth,
    (0, express_validator_1.body)("profile.firstName").optional().notEmpty(),
    (0, express_validator_1.body)("profile.lastName").optional().notEmpty(),
    (0, express_validator_1.body)("profile.phone").optional().isMobilePhone("any"),
    validation_1.validate,
], userController_1.updateUser);
// Delete user (Admin only)
router.delete("/:id", auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), userController_1.deleteUser);
// Create tutor (Admin only)
router.post("/tutors", [
    auth_1.auth,
    (0, auth_1.authorize)(types_1.UserRole.ADMIN),
    (0, express_validator_1.body)("email").isEmail().normalizeEmail(),
    (0, express_validator_1.body)("password").isLength({ min: 8 }),
    (0, express_validator_1.body)("profile.firstName").notEmpty(),
    (0, express_validator_1.body)("profile.lastName").notEmpty(),
    validation_1.validate,
], userController_1.createTutor);
// Update user status (Admin only)
router.patch("/:id/status", [auth_1.auth, (0, auth_1.authorize)(types_1.UserRole.ADMIN), (0, express_validator_1.body)("isActive").isBoolean(), validation_1.validate], userController_1.updateUserStatus);
exports.default = router;
//# sourceMappingURL=user.js.map