events {
    worker_connections 1024;
}

http {
    upstream auth_service {
        server auth-service:3001;
    }

    upstream course_service {
        server course-service:3002;
    }

    upstream payment_service {
        server payment-service:3003;
    }

    upstream analytics_service {
        server analytics-service:3004;
    }

    server {
        listen 80;
        server_name localhost;

        # Auth Service Routes
        location /api/auth/ {
            proxy_pass http://auth_service/api/auth/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Course Service Routes
        location /api/courses/ {
            proxy_pass http://course_service/api/courses/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Payment Service Routes
        location /api/payments/ {
            proxy_pass http://payment_service/api/payments/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Analytics Service Routes
        location /api/analytics/ {
            proxy_pass http://analytics_service/api/analytics/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}