"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.progressService = exports.ProgressService = void 0;
const Progress_1 = require("../models/Progress");
const Course_1 = require("../models/Course");
const Module_1 = require("../models/Module");
const Lesson_1 = require("../models/Lesson");
const Assignment_1 = require("../models/Assignment");
const logger_1 = require("../utils/logger");
class ProgressService {
    /**
     * Initialize progress tracking for a new subscription
     */
    async initializeProgress(studentId, courseId, subscriptionId) {
        try {
            // Get course details
            const course = await Course_1.Course.findById(courseId);
            if (!course) {
                throw new Error('Course not found');
            }
            // Count total lessons and assignments
            const modules = await Module_1.Module.find({ courseId }).sort({ order: 1 });
            const moduleIds = modules.map(m => m.id);
            const lessons = await Lesson_1.Lesson.find({ moduleId: { $in: moduleIds } });
            const assignments = await Assignment_1.Assignment.find({ lessonId: { $in: lessons.map(l => l.id) } });
            // Create progress record
            const progress = new Progress_1.Progress({
                studentId,
                courseId,
                subscriptionId,
                completedLessons: [],
                completedAssignments: [],
                totalLessons: lessons.length,
                totalAssignments: assignments.length,
                completionPercentage: 0,
                currentModule: 1
            });
            await progress.save();
            logger_1.logger.info(`Progress initialized for student ${studentId} in course ${courseId}`);
        }
        catch (error) {
            logger_1.logger.error('Error initializing progress:', error);
            throw error;
        }
    }
    /**
     * Update progress when a lesson is completed
     */
    async completeLesson(studentId, courseId, lessonId) {
        try {
            const progress = await Progress_1.Progress.findOne({ studentId, courseId });
            if (!progress) {
                throw new Error('Progress record not found');
            }
            // Add lesson to completed if not already there
            if (!progress.completedLessons.includes(lessonId)) {
                progress.completedLessons.push(lessonId);
                await this.updateCompletionPercentage(progress);
                await progress.save();
            }
            logger_1.logger.info(`Lesson ${lessonId} completed for student ${studentId}`);
        }
        catch (error) {
            logger_1.logger.error('Error completing lesson:', error);
            throw error;
        }
    }
    /**
     * Update progress when an assignment is completed
     */
    async completeAssignment(studentId, courseId, assignmentId) {
        try {
            const progress = await Progress_1.Progress.findOne({ studentId, courseId });
            if (!progress) {
                throw new Error('Progress record not found');
            }
            // Add assignment to completed if not already there
            if (!progress.completedAssignments.includes(assignmentId)) {
                progress.completedAssignments.push(assignmentId);
                await this.updateCompletionPercentage(progress);
                await progress.save();
            }
            logger_1.logger.info(`Assignment ${assignmentId} completed for student ${studentId}`);
        }
        catch (error) {
            logger_1.logger.error('Error completing assignment:', error);
            throw error;
        }
    }
    /**
     * Calculate pacing status for a student
     */
    async calculatePacing(studentId, courseId, subscriptionStartDate, courseDuration) {
        try {
            const progress = await Progress_1.Progress.findOne({ studentId, courseId });
            if (!progress) {
                throw new Error('Progress record not found');
            }
            const course = await Course_1.Course.findById(courseId);
            if (!course) {
                throw new Error('Course not found');
            }
            // Calculate days elapsed since subscription started
            const now = new Date();
            const daysElapsed = Math.floor((now.getTime() - subscriptionStartDate.getTime()) / (1000 * 60 * 60 * 24));
            // Calculate expected progress based on course duration and total modules
            const expectedProgress = Math.floor((daysElapsed / courseDuration) * course.totalModules);
            // Get actual progress (current module)
            const actualProgress = progress.currentModule - 1; // 0-based for comparison
            const isOnTrack = actualProgress >= expectedProgress;
            const result = {
                isOnTrack,
                expectedProgress,
                actualProgress,
                daysElapsed,
                totalDays: courseDuration
            };
            if (!isOnTrack) {
                result.warningMessage = `You are behind schedule! You should have completed ${expectedProgress} modules by now, but you've only completed ${actualProgress}.`;
            }
            return result;
        }
        catch (error) {
            logger_1.logger.error('Error calculating pacing:', error);
            throw error;
        }
    }
    /**
     * Get detailed progress information
     */
    async getProgressDetails(studentId, courseId) {
        try {
            const progress = await Progress_1.Progress.findOne({ studentId, courseId });
            if (!progress) {
                throw new Error('Progress record not found');
            }
            const course = await Course_1.Course.findById(courseId);
            const modules = await Module_1.Module.find({ courseId }).sort({ order: 1 });
            const progressDetails = {
                ...progress.toObject(),
                course: course?.title,
                modules: []
            };
            // Get progress for each module
            for (const module of modules) {
                const lessons = await Lesson_1.Lesson.find({ moduleId: module.id }).sort({ order: 1 });
                const moduleProgress = {
                    ...module.toObject(),
                    lessons: lessons.map(lesson => ({
                        ...lesson.toObject(),
                        isCompleted: progress.completedLessons.includes(lesson.id)
                    })),
                    completedLessons: lessons.filter(lesson => progress.completedLessons.includes(lesson.id)).length,
                    totalLessons: lessons.length
                };
                progressDetails.modules.push(moduleProgress);
            }
            return progressDetails;
        }
        catch (error) {
            logger_1.logger.error('Error getting progress details:', error);
            throw error;
        }
    }
    /**
     * Update completion percentage
     */
    async updateCompletionPercentage(progress) {
        const totalItems = progress.totalLessons + progress.totalAssignments;
        const completedItems = progress.completedLessons.length + progress.completedAssignments.length;
        progress.completionPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
        // Update current module based on completed lessons
        if (progress.completedLessons.length > 0) {
            // Find which module the student is currently in
            const modules = await Module_1.Module.find({ courseId: progress.courseId }).sort({ order: 1 });
            let currentModule = 1;
            for (const module of modules) {
                const lessons = await Lesson_1.Lesson.find({ moduleId: module.id });
                const completedInModule = lessons.filter(lesson => progress.completedLessons.includes(lesson.id)).length;
                if (completedInModule === lessons.length && lessons.length > 0) {
                    currentModule = module.order + 1;
                }
                else if (completedInModule > 0) {
                    currentModule = module.order;
                    break;
                }
            }
            progress.currentModule = Math.min(currentModule, modules.length);
        }
        // Mark as completed if 100%
        if (progress.completionPercentage === 100 && !progress.completedAt) {
            progress.completedAt = new Date();
        }
    }
}
exports.ProgressService = ProgressService;
exports.progressService = new ProgressService();
//# sourceMappingURL=progressService.js.map