import axios from 'axios';
import crypto from 'crypto';
import { logger } from '../utils/logger';

interface XenditInvoiceRequest {
  external_id: string;
  amount: number;
  description: string;
  invoice_duration: number;
  customer: {
    given_names: string;
    email: string;
  };
  customer_notification_preference: {
    invoice_created: string[];
    invoice_reminder: string[];
    invoice_paid: string[];
  };
  success_redirect_url?: string;
  failure_redirect_url?: string;
}

interface XenditInvoiceResponse {
  id: string;
  external_id: string;
  user_id: string;
  status: string;
  merchant_name: string;
  amount: number;
  description: string;
  invoice_url: string;
  expiry_date: string;
  created: string;
  updated: string;
}

export class XenditService {
  private apiKey: string;
  private webhookToken: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.XENDIT_SECRET_KEY || '';
    this.webhookToken = process.env.XENDIT_WEBHOOK_TOKEN || '';
    this.baseUrl = 'https://api.xendit.co';

    if (!this.apiKey) {
      throw new Error('Xendit API key is required');
    }
  }

  /**
   * Create payment invoice
   */
  async createInvoice(
    externalId: string,
    amount: number,
    description: string,
    customerName: string,
    customerEmail: string,
    duration: number = 86400 // 24 hours in seconds
  ): Promise<XenditInvoiceResponse> {
    try {
      const invoiceData: XenditInvoiceRequest = {
        external_id: externalId,
        amount: amount,
        description: description,
        invoice_duration: duration,
        customer: {
          given_names: customerName,
          email: customerEmail
        },
        customer_notification_preference: {
          invoice_created: ['email'],
          invoice_reminder: ['email'],
          invoice_paid: ['email']
        },
        success_redirect_url: `${process.env.FRONTEND_URL}/payment/success`,
        failure_redirect_url: `${process.env.FRONTEND_URL}/payment/failed`
      };

      const response = await axios.post(
        `${this.baseUrl}/v2/invoices`,
        invoiceData,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.apiKey + ':').toString('base64')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      logger.info(`Invoice created: ${response.data.id} for external_id: ${externalId}`);
      return response.data;
    } catch (error: any) {
      logger.error('Error creating Xendit invoice:', error.response?.data || error.message);
      throw new Error('Failed to create payment invoice');
    }
  }

  /**
   * Get invoice by ID
   */
  async getInvoice(invoiceId: string): Promise<XenditInvoiceResponse> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/v2/invoices/${invoiceId}`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.apiKey + ':').toString('base64')}`
          }
        }
      );

      return response.data;
    } catch (error: any) {
      logger.error('Error getting Xendit invoice:', error.response?.data || error.message);
      throw new Error('Failed to get invoice');
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(rawBody: string, signature: string): boolean {
    try {
      const computedSignature = crypto
        .createHmac('sha256', this.webhookToken)
        .update(rawBody)
        .digest('hex');

      return signature === computedSignature;
    } catch (error) {
      logger.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  /**
   * Process webhook payload
   */
  processWebhook(payload: any): {
    event: string;
    invoiceId: string;
    externalId: string;
    status: string;
    amount: number;
    paidAt?: Date;
  } {
    return {
      event: payload.event || 'invoice.paid',
      invoiceId: payload.id,
      externalId: payload.external_id,
      status: payload.status,
      amount: payload.amount,
      paidAt: payload.paid_at ? new Date(payload.paid_at) : undefined
    };
  }
}

export const xenditService = new XenditService();
