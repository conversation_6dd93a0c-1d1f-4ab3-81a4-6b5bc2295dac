{"name": "auth-service", "version": "1.0.0", "description": "Authentication and User Management Service", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "firebase-admin": "^12.0.0", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}